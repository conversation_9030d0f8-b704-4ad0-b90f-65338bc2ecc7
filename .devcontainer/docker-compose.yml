version: '3.7'

services:
  devcontainer:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ..:/workspace:cached
      # Mount the TestConfig.json file as readonly, so that tests talk to services in the internal docker network
      # This leaves the original TestsConfig.json outside the devcontainer untouched
      - ./TestConfig.json:/workspace/tests/StackExchange.Redis.Tests/TestConfig.json:ro
    depends_on:
      - redis
    links:
      - "redis:redis"
    command: /bin/sh -c "while sleep 1000; do :; done"
  redis:
    build:
      context: ../tests/RedisConfigs
      dockerfile: Dockerfile
    sysctls :
      net.core.somaxconn: '511'