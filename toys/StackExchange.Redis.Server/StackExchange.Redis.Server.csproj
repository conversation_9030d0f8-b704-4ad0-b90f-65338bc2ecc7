﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0</TargetFrameworks>
    <Description>Basic redis server based on StackExchange.Redis</Description>
    <AssemblyTitle>StackExchange.Redis</AssemblyTitle>
    <AssemblyName>StackExchange.Redis.Server</AssemblyName>
    <PackageId>StackExchange.Redis.Server</PackageId>
    <PackageTags>Server;Async;Redis;Cache;PubSub;Messaging</PackageTags>
    <OutputTypeEx>Library</OutputTypeEx>
    <SignAssembly>true</SignAssembly>
    <PublicSign Condition=" '$(OS)' != 'Windows_NT' ">true</PublicSign>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\StackExchange.Redis\StackExchange.Redis.csproj" />
    <PackageReference Include="System.Runtime.Caching" />
  </ItemGroup>
</Project>
