﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Nullable>enable</Nullable>
    <!-- extend the default lib targets for the main lib; mostly because of "vectors" -->
    <TargetFrameworks>net461;netstandard2.0;net472;netcoreapp3.1;net6.0;net8.0</TargetFrameworks>
    <Description>High performance Redis client, incorporating both synchronous and asynchronous usage.</Description>
    <AssemblyName>StackExchange.Redis</AssemblyName>
    <AssemblyTitle>StackExchange.Redis</AssemblyTitle>
    <PackageId>StackExchange.Redis</PackageId>
    <PackageTags>Async;Redis;Cache;PubSub;Messaging</PackageTags>
    <SignAssembly>true</SignAssembly>
    <PublicSign Condition=" '$(OS)' != 'Windows_NT' ">true</PublicSign>
    <DefineConstants Condition="'$(TargetFramework)' != 'net461'">$(DefineConstants);VECTOR_SAFE</DefineConstants>
    <DefineConstants Condition="'$(TargetFramework)' != 'net461' and '$(TargetFramework)' != 'net472' and '$(TargetFramework)' != 'netstandard2.0'">$(DefineConstants);UNIX_SOCKET</DefineConstants>
    <PackageReadmeFile>README.md</PackageReadmeFile>
  </PropertyGroup>

  <ItemGroup>
    <!-- needed everywhere -->
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Pipelines.Sockets.Unofficial" />

    <!-- built into .NET core now -->
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Condition="'$(TargetFramework)' == 'net472' or '$(TargetFramework)' == 'net461' or '$(TargetFramework)' == 'netstandard2.0'" />
    <PackageReference Include="System.Threading.Channels" Condition="'$(TargetFramework)' == 'net472' or '$(TargetFramework)' == 'net461' or '$(TargetFramework)' == 'netstandard2.0'" />

    <!-- net461 needs this for OSPlatform et al -->
    <PackageReference Include="System.Runtime.InteropServices.RuntimeInformation" Condition="'$(TargetFramework)' == 'net461' " />
    
    <!-- netfx needs this for ZipArchive -->
    <PackageReference Include="System.IO.Compression" Condition="'$(TargetFramework)' == 'net472' or '$(TargetFramework)' == 'net461' " />

    <None Include="README.md" Pack="true" PackagePath="\"/>
  </ItemGroup>

  <ItemGroup>
    <!-- APIs for all target frameworks -->
    <AdditionalFiles Include="PublicAPI/PublicAPI.Shipped.txt" />
    <AdditionalFiles Include="PublicAPI/PublicAPI.Unshipped.txt" />
    <!-- APIs for netcoreapp3.1+ -->
    <AdditionalFiles Include="PublicAPI/$(TargetFramework)/PublicAPI.Shipped.txt" Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp'" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="StackExchange.Redis.Server" />
    <InternalsVisibleTo Include="StackExchange.Redis.Tests" />
  </ItemGroup>
</Project>