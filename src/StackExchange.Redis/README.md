StackExchange.Redis is a high-performance RESP (Redis, etc) client for .NET, available under the MIT license.

- Release notes: [https://stackexchange.github.io/StackExchange.Redis/ReleaseNotes](https://stackexchange.github.io/StackExchange.Redis/ReleaseNotes)
- NuGet package: [https://www.nuget.org/packages/StackExchange.Redis/](https://www.nuget.org/packages/StackExchange.Redis/)
- General docs: [https://stackexchange.github.io/StackExchange.Redis/](https://stackexchange.github.io/StackExchange.Redis/)
- Code: [https://github.com/StackExchange/StackExchange.Redis/](https://github.com/StackExchange/StackExchange.Redis/)