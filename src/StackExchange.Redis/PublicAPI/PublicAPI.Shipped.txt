﻿#nullable enable
abstract StackExchange.Redis.RedisResult.IsNull.get -> bool
override StackExchange.Redis.ChannelMessage.Equals(object? obj) -> bool
override StackExchange.Redis.ChannelMessage.GetHashCode() -> int
override StackExchange.Redis.ChannelMessage.ToString() -> string!
override StackExchange.Redis.ChannelMessageQueue.ToString() -> string?
override StackExchange.Redis.ClientInfo.ToString() -> string!
override StackExchange.Redis.ClusterNode.Equals(object? obj) -> bool
override StackExchange.Redis.ClusterNode.GetHashCode() -> int
override StackExchange.Redis.ClusterNode.ToString() -> string!
override StackExchange.Redis.CommandMap.ToString() -> string!
override StackExchange.Redis.Configuration.AzureOptionsProvider.AbortOnConnectFail.get -> bool
override StackExchange.Redis.Configuration.AzureOptionsProvider.AfterConnectAsync(StackExchange.Redis.ConnectionMultiplexer! muxer, System.Action<string!>! log) -> System.Threading.Tasks.Task!
override StackExchange.Redis.Configuration.AzureOptionsProvider.DefaultVersion.get -> System.Version!
override StackExchange.Redis.Configuration.AzureOptionsProvider.GetDefaultSsl(StackExchange.Redis.EndPointCollection! endPoints) -> bool
override StackExchange.Redis.Configuration.AzureOptionsProvider.IsMatch(System.Net.EndPoint! endpoint) -> bool
override StackExchange.Redis.ConfigurationOptions.ToString() -> string!
override StackExchange.Redis.ConnectionCounters.ToString() -> string!
override StackExchange.Redis.ConnectionFailedEventArgs.ToString() -> string!
override StackExchange.Redis.ConnectionMultiplexer.ToString() -> string!
override StackExchange.Redis.GeoEntry.Equals(object? obj) -> bool
override StackExchange.Redis.GeoEntry.GetHashCode() -> int
override StackExchange.Redis.GeoEntry.ToString() -> string!
override StackExchange.Redis.GeoPosition.Equals(object? obj) -> bool
override StackExchange.Redis.GeoPosition.GetHashCode() -> int
override StackExchange.Redis.GeoPosition.ToString() -> string!
override StackExchange.Redis.GeoRadiusResult.ToString() -> string!
override StackExchange.Redis.HashEntry.Equals(object? obj) -> bool
override StackExchange.Redis.HashEntry.GetHashCode() -> int
override StackExchange.Redis.HashEntry.ToString() -> string!
override StackExchange.Redis.Maintenance.ServerMaintenanceEvent.ToString() -> string?
override StackExchange.Redis.NameValueEntry.Equals(object? obj) -> bool
override StackExchange.Redis.NameValueEntry.GetHashCode() -> int
override StackExchange.Redis.NameValueEntry.ToString() -> string!
override StackExchange.Redis.RedisChannel.Equals(object? obj) -> bool
override StackExchange.Redis.RedisChannel.GetHashCode() -> int
override StackExchange.Redis.RedisChannel.ToString() -> string!
override StackExchange.Redis.RedisConnectionException.GetObjectData(System.Runtime.Serialization.SerializationInfo! info, System.Runtime.Serialization.StreamingContext context) -> void
override StackExchange.Redis.RedisFeatures.Equals(object? obj) -> bool
override StackExchange.Redis.RedisFeatures.GetHashCode() -> int
override StackExchange.Redis.RedisFeatures.ToString() -> string!
override StackExchange.Redis.RedisKey.Equals(object? obj) -> bool
override StackExchange.Redis.RedisKey.GetHashCode() -> int
override StackExchange.Redis.RedisKey.ToString() -> string!
override StackExchange.Redis.RedisTimeoutException.GetObjectData(System.Runtime.Serialization.SerializationInfo! info, System.Runtime.Serialization.StreamingContext context) -> void
override StackExchange.Redis.RedisValue.Equals(object? obj) -> bool
override StackExchange.Redis.RedisValue.GetHashCode() -> int
override StackExchange.Redis.RedisValue.ToString() -> string!
override StackExchange.Redis.Role.ToString() -> string!
override StackExchange.Redis.ServerCounters.ToString() -> string!
override StackExchange.Redis.SlotRange.Equals(object? obj) -> bool
override StackExchange.Redis.SlotRange.GetHashCode() -> int
override StackExchange.Redis.SlotRange.ToString() -> string!
override StackExchange.Redis.SocketManager.ToString() -> string!
override StackExchange.Redis.SortedSetEntry.Equals(object? obj) -> bool
override StackExchange.Redis.SortedSetEntry.GetHashCode() -> int
override StackExchange.Redis.SortedSetEntry.ToString() -> string!
StackExchange.Redis.Aggregate
StackExchange.Redis.Aggregate.Max = 2 -> StackExchange.Redis.Aggregate
StackExchange.Redis.Aggregate.Min = 1 -> StackExchange.Redis.Aggregate
StackExchange.Redis.Aggregate.Sum = 0 -> StackExchange.Redis.Aggregate
StackExchange.Redis.BacklogPolicy
StackExchange.Redis.BacklogPolicy.AbortPendingOnConnectionFailure.get -> bool
StackExchange.Redis.BacklogPolicy.AbortPendingOnConnectionFailure.init -> void
StackExchange.Redis.BacklogPolicy.BacklogPolicy() -> void
StackExchange.Redis.BacklogPolicy.QueueWhileDisconnected.get -> bool
StackExchange.Redis.BacklogPolicy.QueueWhileDisconnected.init -> void
StackExchange.Redis.Bitwise
StackExchange.Redis.Bitwise.And = 0 -> StackExchange.Redis.Bitwise
StackExchange.Redis.Bitwise.Not = 3 -> StackExchange.Redis.Bitwise
StackExchange.Redis.Bitwise.Or = 1 -> StackExchange.Redis.Bitwise
StackExchange.Redis.Bitwise.Xor = 2 -> StackExchange.Redis.Bitwise
StackExchange.Redis.ChannelMessage
StackExchange.Redis.ChannelMessage.Channel.get -> StackExchange.Redis.RedisChannel
StackExchange.Redis.ChannelMessage.ChannelMessage() -> void
StackExchange.Redis.ChannelMessage.Message.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.ChannelMessage.SubscriptionChannel.get -> StackExchange.Redis.RedisChannel
StackExchange.Redis.ChannelMessageQueue
StackExchange.Redis.ChannelMessageQueue.Channel.get -> StackExchange.Redis.RedisChannel
StackExchange.Redis.ChannelMessageQueue.Completion.get -> System.Threading.Tasks.Task!
StackExchange.Redis.ChannelMessageQueue.GetAsyncEnumerator(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken)) -> System.Collections.Generic.IAsyncEnumerator<StackExchange.Redis.ChannelMessage>!
StackExchange.Redis.ChannelMessageQueue.OnMessage(System.Action<StackExchange.Redis.ChannelMessage>! handler) -> void
StackExchange.Redis.ChannelMessageQueue.OnMessage(System.Func<StackExchange.Redis.ChannelMessage, System.Threading.Tasks.Task!>! handler) -> void
StackExchange.Redis.ChannelMessageQueue.ReadAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken)) -> System.Threading.Tasks.ValueTask<StackExchange.Redis.ChannelMessage>
StackExchange.Redis.ChannelMessageQueue.TryGetCount(out int count) -> bool
StackExchange.Redis.ChannelMessageQueue.TryRead(out StackExchange.Redis.ChannelMessage item) -> bool
StackExchange.Redis.ChannelMessageQueue.Unsubscribe(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.ChannelMessageQueue.UnsubscribeAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.Blocked = 16 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.BroadcastTracking = 16384 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.CloseASAP = 256 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.Closing = 64 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.KeysTracking = 4096 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.Master = 4 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.None = 0 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.PubSubSubscriber = 512 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.ReadOnlyCluster = 1024 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.Replica = 2 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.ReplicaMonitor = 1 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.Slave = 2 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.SlaveMonitor = 1 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.TrackingTargetInvalid = 8192 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.Transaction = 8 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.TransactionDoomed = 32 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.Unblocked = 128 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientFlags.UnixDomainSocket = 2048 -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientInfo
StackExchange.Redis.ClientInfo.Address.get -> System.Net.EndPoint?
StackExchange.Redis.ClientInfo.AgeSeconds.get -> int
StackExchange.Redis.ClientInfo.ClientInfo() -> void
StackExchange.Redis.ClientInfo.ClientType.get -> StackExchange.Redis.ClientType
StackExchange.Redis.ClientInfo.Database.get -> int
StackExchange.Redis.ClientInfo.Flags.get -> StackExchange.Redis.ClientFlags
StackExchange.Redis.ClientInfo.FlagsRaw.get -> string?
StackExchange.Redis.ClientInfo.Host.get -> string?
StackExchange.Redis.ClientInfo.Id.get -> long
StackExchange.Redis.ClientInfo.IdleSeconds.get -> int
StackExchange.Redis.ClientInfo.LastCommand.get -> string?
StackExchange.Redis.ClientInfo.LibraryName.get -> string?
StackExchange.Redis.ClientInfo.LibraryVersion.get -> string?
StackExchange.Redis.ClientInfo.Name.get -> string?
StackExchange.Redis.ClientInfo.PatternSubscriptionCount.get -> int
StackExchange.Redis.ClientInfo.Port.get -> int
StackExchange.Redis.ClientInfo.ProtocolVersion.get -> string?
StackExchange.Redis.ClientInfo.Raw.get -> string?
StackExchange.Redis.ClientInfo.SubscriptionCount.get -> int
StackExchange.Redis.ClientInfo.TransactionCommandLength.get -> int
StackExchange.Redis.ClientKillFilter
StackExchange.Redis.ClientKillFilter.ClientKillFilter() -> void
StackExchange.Redis.ClientKillFilter.ClientType.get -> StackExchange.Redis.ClientType?
StackExchange.Redis.ClientKillFilter.Endpoint.get -> System.Net.EndPoint?
StackExchange.Redis.ClientKillFilter.Id.get -> long?
StackExchange.Redis.ClientKillFilter.MaxAgeInSeconds.get -> long?
StackExchange.Redis.ClientKillFilter.ServerEndpoint.get -> System.Net.EndPoint?
StackExchange.Redis.ClientKillFilter.SkipMe.get -> bool?
StackExchange.Redis.ClientKillFilter.Username.get -> string?
StackExchange.Redis.ClientKillFilter.WithClientType(StackExchange.Redis.ClientType? clientType) -> StackExchange.Redis.ClientKillFilter!
StackExchange.Redis.ClientKillFilter.WithEndpoint(System.Net.EndPoint? endpoint) -> StackExchange.Redis.ClientKillFilter!
StackExchange.Redis.ClientKillFilter.WithId(long? id) -> StackExchange.Redis.ClientKillFilter!
StackExchange.Redis.ClientKillFilter.WithMaxAgeInSeconds(long? maxAgeInSeconds) -> StackExchange.Redis.ClientKillFilter!
StackExchange.Redis.ClientKillFilter.WithServerEndpoint(System.Net.EndPoint? serverEndpoint) -> StackExchange.Redis.ClientKillFilter!
StackExchange.Redis.ClientKillFilter.WithSkipMe(bool? skipMe) -> StackExchange.Redis.ClientKillFilter!
StackExchange.Redis.ClientKillFilter.WithUsername(string? username) -> StackExchange.Redis.ClientKillFilter!
StackExchange.Redis.ClientType
StackExchange.Redis.ClientType.Normal = 0 -> StackExchange.Redis.ClientType
StackExchange.Redis.ClientType.PubSub = 2 -> StackExchange.Redis.ClientType
StackExchange.Redis.ClientType.Replica = 1 -> StackExchange.Redis.ClientType
StackExchange.Redis.ClientType.Slave = 1 -> StackExchange.Redis.ClientType
StackExchange.Redis.ClusterConfiguration
StackExchange.Redis.ClusterConfiguration.GetBySlot(int slot) -> StackExchange.Redis.ClusterNode?
StackExchange.Redis.ClusterConfiguration.GetBySlot(StackExchange.Redis.RedisKey key) -> StackExchange.Redis.ClusterNode?
StackExchange.Redis.ClusterConfiguration.Nodes.get -> System.Collections.Generic.ICollection<StackExchange.Redis.ClusterNode!>!
StackExchange.Redis.ClusterConfiguration.Origin.get -> System.Net.EndPoint!
StackExchange.Redis.ClusterConfiguration.this[System.Net.EndPoint! endpoint].get -> StackExchange.Redis.ClusterNode?
StackExchange.Redis.ClusterNode
StackExchange.Redis.ClusterNode.Children.get -> System.Collections.Generic.IList<StackExchange.Redis.ClusterNode!>!
StackExchange.Redis.ClusterNode.CompareTo(StackExchange.Redis.ClusterNode? other) -> int
StackExchange.Redis.ClusterNode.EndPoint.get -> System.Net.EndPoint?
StackExchange.Redis.ClusterNode.Equals(StackExchange.Redis.ClusterNode? other) -> bool
StackExchange.Redis.ClusterNode.IsConnected.get -> bool
StackExchange.Redis.ClusterNode.IsFail.get -> bool
StackExchange.Redis.ClusterNode.IsMyself.get -> bool
StackExchange.Redis.ClusterNode.IsNoAddr.get -> bool
StackExchange.Redis.ClusterNode.IsPossiblyFail.get -> bool
StackExchange.Redis.ClusterNode.IsReplica.get -> bool
StackExchange.Redis.ClusterNode.IsSlave.get -> bool
StackExchange.Redis.ClusterNode.NodeId.get -> string!
StackExchange.Redis.ClusterNode.Parent.get -> StackExchange.Redis.ClusterNode?
StackExchange.Redis.ClusterNode.ParentNodeId.get -> string?
StackExchange.Redis.ClusterNode.Raw.get -> string!
StackExchange.Redis.ClusterNode.Slots.get -> System.Collections.Generic.IList<StackExchange.Redis.SlotRange>!
StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.DemandMaster = 4 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.DemandReplica = StackExchange.Redis.CommandFlags.DemandMaster | StackExchange.Redis.CommandFlags.PreferReplica -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.DemandSlave = StackExchange.Redis.CommandFlags.DemandMaster | StackExchange.Redis.CommandFlags.PreferReplica -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.FireAndForget = 2 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.HighPriority = 1 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.None = 0 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.NoRedirect = 64 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.NoScriptCache = 512 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.PreferMaster = 0 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.PreferReplica = 8 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandFlags.PreferSlave = 8 -> StackExchange.Redis.CommandFlags
StackExchange.Redis.CommandMap
StackExchange.Redis.CommandStatus
StackExchange.Redis.CommandStatus.Sent = 2 -> StackExchange.Redis.CommandStatus
StackExchange.Redis.CommandStatus.Unknown = 0 -> StackExchange.Redis.CommandStatus
StackExchange.Redis.CommandStatus.WaitingToBeSent = 1 -> StackExchange.Redis.CommandStatus
StackExchange.Redis.CommandStatus.WaitingInBacklog = 3 -> StackExchange.Redis.CommandStatus
StackExchange.Redis.CommandTrace
StackExchange.Redis.CommandTrace.Arguments.get -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.CommandTrace.Duration.get -> System.TimeSpan
StackExchange.Redis.CommandTrace.GetHelpUrl() -> string?
StackExchange.Redis.CommandTrace.Time.get -> System.DateTime
StackExchange.Redis.CommandTrace.UniqueId.get -> long
StackExchange.Redis.Condition
StackExchange.Redis.ConditionResult
StackExchange.Redis.ConditionResult.WasSatisfied.get -> bool
StackExchange.Redis.Configuration.AzureOptionsProvider
StackExchange.Redis.Configuration.AzureOptionsProvider.AzureOptionsProvider() -> void
StackExchange.Redis.Configuration.DefaultOptionsProvider
StackExchange.Redis.Configuration.DefaultOptionsProvider.ClientName.get -> string!
StackExchange.Redis.Configuration.DefaultOptionsProvider.DefaultOptionsProvider() -> void
StackExchange.Redis.Configuration.Tunnel
StackExchange.Redis.Configuration.Tunnel.Tunnel() -> void
static StackExchange.Redis.Configuration.Tunnel.HttpProxy(System.Net.EndPoint! proxy) -> StackExchange.Redis.Configuration.Tunnel!
virtual StackExchange.Redis.Configuration.Tunnel.BeforeAuthenticateAsync(System.Net.EndPoint! endpoint, StackExchange.Redis.ConnectionType connectionType, System.Net.Sockets.Socket? socket, System.Threading.CancellationToken cancellationToken) -> System.Threading.Tasks.ValueTask<System.IO.Stream?>
virtual StackExchange.Redis.Configuration.Tunnel.BeforeSocketConnectAsync(System.Net.EndPoint! endPoint, StackExchange.Redis.ConnectionType connectionType, System.Net.Sockets.Socket? socket, System.Threading.CancellationToken cancellationToken) -> System.Threading.Tasks.ValueTask
virtual StackExchange.Redis.Configuration.Tunnel.GetSocketConnectEndpointAsync(System.Net.EndPoint! endpoint, System.Threading.CancellationToken cancellationToken) -> System.Threading.Tasks.ValueTask<System.Net.EndPoint?>
StackExchange.Redis.ConfigurationOptions
StackExchange.Redis.ConfigurationOptions.AbortOnConnectFail.get -> bool
StackExchange.Redis.ConfigurationOptions.AbortOnConnectFail.set -> void
StackExchange.Redis.ConfigurationOptions.AllowAdmin.get -> bool
StackExchange.Redis.ConfigurationOptions.AllowAdmin.set -> void
StackExchange.Redis.ConfigurationOptions.Apply(System.Action<StackExchange.Redis.ConfigurationOptions!>! configure) -> StackExchange.Redis.ConfigurationOptions!
StackExchange.Redis.ConfigurationOptions.AsyncTimeout.get -> int
StackExchange.Redis.ConfigurationOptions.AsyncTimeout.set -> void
StackExchange.Redis.ConfigurationOptions.BacklogPolicy.get -> StackExchange.Redis.BacklogPolicy!
StackExchange.Redis.ConfigurationOptions.BacklogPolicy.set -> void
StackExchange.Redis.ConfigurationOptions.BeforeSocketConnect.get -> System.Action<System.Net.EndPoint!, StackExchange.Redis.ConnectionType, System.Net.Sockets.Socket!>?
StackExchange.Redis.ConfigurationOptions.BeforeSocketConnect.set -> void
StackExchange.Redis.ConfigurationOptions.CertificateSelection -> System.Net.Security.LocalCertificateSelectionCallback?
StackExchange.Redis.ConfigurationOptions.CertificateValidation -> System.Net.Security.RemoteCertificateValidationCallback?
StackExchange.Redis.ConfigurationOptions.ChannelPrefix.get -> StackExchange.Redis.RedisChannel
StackExchange.Redis.ConfigurationOptions.ChannelPrefix.set -> void
StackExchange.Redis.ConfigurationOptions.CheckCertificateRevocation.get -> bool
StackExchange.Redis.ConfigurationOptions.CheckCertificateRevocation.set -> void
StackExchange.Redis.ConfigurationOptions.ClientName.get -> string?
StackExchange.Redis.ConfigurationOptions.ClientName.set -> void
StackExchange.Redis.ConfigurationOptions.Clone() -> StackExchange.Redis.ConfigurationOptions!
StackExchange.Redis.ConfigurationOptions.CommandMap.get -> StackExchange.Redis.CommandMap!
StackExchange.Redis.ConfigurationOptions.CommandMap.set -> void
StackExchange.Redis.ConfigurationOptions.ConfigCheckSeconds.get -> int
StackExchange.Redis.ConfigurationOptions.ConfigCheckSeconds.set -> void
StackExchange.Redis.ConfigurationOptions.ConfigurationChannel.get -> string!
StackExchange.Redis.ConfigurationOptions.ConfigurationChannel.set -> void
StackExchange.Redis.ConfigurationOptions.ConfigurationOptions() -> void
StackExchange.Redis.ConfigurationOptions.ConnectRetry.get -> int
StackExchange.Redis.ConfigurationOptions.ConnectRetry.set -> void
StackExchange.Redis.ConfigurationOptions.ConnectTimeout.get -> int
StackExchange.Redis.ConfigurationOptions.ConnectTimeout.set -> void
StackExchange.Redis.ConfigurationOptions.DefaultDatabase.get -> int?
StackExchange.Redis.ConfigurationOptions.DefaultDatabase.set -> void
StackExchange.Redis.ConfigurationOptions.Defaults.get -> StackExchange.Redis.Configuration.DefaultOptionsProvider!
StackExchange.Redis.ConfigurationOptions.Defaults.set -> void
StackExchange.Redis.ConfigurationOptions.DefaultVersion.get -> System.Version!
StackExchange.Redis.ConfigurationOptions.DefaultVersion.set -> void
StackExchange.Redis.ConfigurationOptions.EndPoints.get -> StackExchange.Redis.EndPointCollection!
StackExchange.Redis.ConfigurationOptions.EndPoints.init -> void
StackExchange.Redis.ConfigurationOptions.HeartbeatConsistencyChecks.get -> bool
StackExchange.Redis.ConfigurationOptions.HeartbeatConsistencyChecks.set -> void
StackExchange.Redis.ConfigurationOptions.HeartbeatInterval.get -> System.TimeSpan
StackExchange.Redis.ConfigurationOptions.HeartbeatInterval.set -> void
StackExchange.Redis.ConfigurationOptions.HighIntegrity.get -> bool
StackExchange.Redis.ConfigurationOptions.HighIntegrity.set -> void
StackExchange.Redis.ConfigurationOptions.HighPrioritySocketThreads.get -> bool
StackExchange.Redis.ConfigurationOptions.HighPrioritySocketThreads.set -> void
StackExchange.Redis.ConfigurationOptions.IncludeDetailInExceptions.get -> bool
StackExchange.Redis.ConfigurationOptions.IncludeDetailInExceptions.set -> void
StackExchange.Redis.ConfigurationOptions.IncludePerformanceCountersInExceptions.get -> bool
StackExchange.Redis.ConfigurationOptions.IncludePerformanceCountersInExceptions.set -> void
StackExchange.Redis.ConfigurationOptions.KeepAlive.get -> int
StackExchange.Redis.ConfigurationOptions.KeepAlive.set -> void
StackExchange.Redis.ConfigurationOptions.LibraryName.get -> string?
StackExchange.Redis.ConfigurationOptions.LibraryName.set -> void
StackExchange.Redis.ConfigurationOptions.LoggerFactory.get -> Microsoft.Extensions.Logging.ILoggerFactory?
StackExchange.Redis.ConfigurationOptions.LoggerFactory.set -> void
StackExchange.Redis.ConfigurationOptions.Password.get -> string?
StackExchange.Redis.ConfigurationOptions.Password.set -> void
StackExchange.Redis.ConfigurationOptions.PreserveAsyncOrder.get -> bool
StackExchange.Redis.ConfigurationOptions.PreserveAsyncOrder.set -> void
StackExchange.Redis.ConfigurationOptions.Proxy.get -> StackExchange.Redis.Proxy
StackExchange.Redis.ConfigurationOptions.Proxy.set -> void
StackExchange.Redis.ConfigurationOptions.ReconnectRetryPolicy.get -> StackExchange.Redis.IReconnectRetryPolicy!
StackExchange.Redis.ConfigurationOptions.ReconnectRetryPolicy.set -> void
StackExchange.Redis.ConfigurationOptions.ResolveDns.get -> bool
StackExchange.Redis.ConfigurationOptions.ResolveDns.set -> void
StackExchange.Redis.ConfigurationOptions.ResponseTimeout.get -> int
StackExchange.Redis.ConfigurationOptions.ResponseTimeout.set -> void
StackExchange.Redis.ConfigurationOptions.ServiceName.get -> string?
StackExchange.Redis.ConfigurationOptions.ServiceName.set -> void
StackExchange.Redis.ConfigurationOptions.SetClientLibrary.get -> bool
StackExchange.Redis.ConfigurationOptions.SetClientLibrary.set -> void
StackExchange.Redis.ConfigurationOptions.SetDefaultPorts() -> void
StackExchange.Redis.ConfigurationOptions.SocketManager.get -> StackExchange.Redis.SocketManager?
StackExchange.Redis.ConfigurationOptions.SocketManager.set -> void
StackExchange.Redis.ConfigurationOptions.Ssl.get -> bool
StackExchange.Redis.ConfigurationOptions.Ssl.set -> void
StackExchange.Redis.ConfigurationOptions.SslHost.get -> string?
StackExchange.Redis.ConfigurationOptions.SslHost.set -> void
StackExchange.Redis.ConfigurationOptions.SslProtocols.get -> System.Security.Authentication.SslProtocols?
StackExchange.Redis.ConfigurationOptions.SslProtocols.set -> void
StackExchange.Redis.ConfigurationOptions.SyncTimeout.get -> int
StackExchange.Redis.ConfigurationOptions.SyncTimeout.set -> void
StackExchange.Redis.ConfigurationOptions.TieBreaker.get -> string!
StackExchange.Redis.ConfigurationOptions.TieBreaker.set -> void
StackExchange.Redis.ConfigurationOptions.ToString(bool includePassword) -> string!
StackExchange.Redis.ConfigurationOptions.TrustIssuer(string! issuerCertificatePath) -> void
StackExchange.Redis.ConfigurationOptions.TrustIssuer(System.Security.Cryptography.X509Certificates.X509Certificate2! issuer) -> void
StackExchange.Redis.ConfigurationOptions.Tunnel.get -> StackExchange.Redis.Configuration.Tunnel?
StackExchange.Redis.ConfigurationOptions.Tunnel.set -> void
StackExchange.Redis.ConfigurationOptions.User.get -> string?
StackExchange.Redis.ConfigurationOptions.User.set -> void
StackExchange.Redis.ConfigurationOptions.UseSsl.get -> bool
StackExchange.Redis.ConfigurationOptions.UseSsl.set -> void
StackExchange.Redis.ConfigurationOptions.WriteBuffer.get -> int
StackExchange.Redis.ConfigurationOptions.WriteBuffer.set -> void
StackExchange.Redis.ConnectionCounters
StackExchange.Redis.ConnectionCounters.CompletedAsynchronously.get -> long
StackExchange.Redis.ConnectionCounters.CompletedSynchronously.get -> long
StackExchange.Redis.ConnectionCounters.ConnectionType.get -> StackExchange.Redis.ConnectionType
StackExchange.Redis.ConnectionCounters.FailedAsynchronously.get -> long
StackExchange.Redis.ConnectionCounters.IsEmpty.get -> bool
StackExchange.Redis.ConnectionCounters.NonPreferredEndpointCount.get -> long
StackExchange.Redis.ConnectionCounters.OperationCount.get -> long
StackExchange.Redis.ConnectionCounters.PendingUnsentItems.get -> int
StackExchange.Redis.ConnectionCounters.ResponsesAwaitingAsyncCompletion.get -> int
StackExchange.Redis.ConnectionCounters.SentItemsAwaitingResponse.get -> int
StackExchange.Redis.ConnectionCounters.SocketCount.get -> long
StackExchange.Redis.ConnectionCounters.Subscriptions.get -> long
StackExchange.Redis.ConnectionCounters.TotalOutstanding.get -> int
StackExchange.Redis.ConnectionCounters.WriterCount.get -> int
StackExchange.Redis.ConnectionFailedEventArgs
StackExchange.Redis.ConnectionFailedEventArgs.ConnectionFailedEventArgs(object! sender, System.Net.EndPoint! endPoint, StackExchange.Redis.ConnectionType connectionType, StackExchange.Redis.ConnectionFailureType failureType, System.Exception! exception, string! physicalName) -> void
StackExchange.Redis.ConnectionFailedEventArgs.ConnectionType.get -> StackExchange.Redis.ConnectionType
StackExchange.Redis.ConnectionFailedEventArgs.EndPoint.get -> System.Net.EndPoint?
StackExchange.Redis.ConnectionFailedEventArgs.Exception.get -> System.Exception?
StackExchange.Redis.ConnectionFailedEventArgs.FailureType.get -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.AuthenticationFailure = 3 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.ConnectionDisposed = 7 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.InternalFailure = 5 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.Loading = 8 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.None = 0 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.ProtocolFailure = 4 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.SocketClosed = 6 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.SocketFailure = 2 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.UnableToConnect = 9 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.ResponseIntegrityFailure = 10 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionFailureType.UnableToResolvePhysicalConnection = 1 -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.ConnectionMultiplexer
StackExchange.Redis.ConnectionMultiplexer.ClientName.get -> string!
StackExchange.Redis.ConnectionMultiplexer.Close(bool allowCommandsToComplete = true) -> void
StackExchange.Redis.ConnectionMultiplexer.CloseAsync(bool allowCommandsToComplete = true) -> System.Threading.Tasks.Task!
StackExchange.Redis.ConnectionMultiplexer.Configuration.get -> string!
StackExchange.Redis.ConnectionMultiplexer.ConfigurationChanged -> System.EventHandler<StackExchange.Redis.EndPointEventArgs!>?
StackExchange.Redis.ConnectionMultiplexer.ConfigurationChangedBroadcast -> System.EventHandler<StackExchange.Redis.EndPointEventArgs!>?
StackExchange.Redis.ConnectionMultiplexer.Configure(System.IO.TextWriter? log = null) -> bool
StackExchange.Redis.ConnectionMultiplexer.ConfigureAsync(System.IO.TextWriter? log = null) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.ConnectionMultiplexer.ConnectionFailed -> System.EventHandler<StackExchange.Redis.ConnectionFailedEventArgs!>?
StackExchange.Redis.ConnectionMultiplexer.ConnectionRestored -> System.EventHandler<StackExchange.Redis.ConnectionFailedEventArgs!>?
StackExchange.Redis.ConnectionMultiplexer.Dispose() -> void
StackExchange.Redis.ConnectionMultiplexer.DisposeAsync() -> System.Threading.Tasks.ValueTask
StackExchange.Redis.ConnectionMultiplexer.ErrorMessage -> System.EventHandler<StackExchange.Redis.RedisErrorEventArgs!>?
StackExchange.Redis.ConnectionMultiplexer.ExportConfiguration(System.IO.Stream! destination, StackExchange.Redis.ExportOptions options = (StackExchange.Redis.ExportOptions)-1) -> void
StackExchange.Redis.ConnectionMultiplexer.GetCounters() -> StackExchange.Redis.ServerCounters!
StackExchange.Redis.ConnectionMultiplexer.GetDatabase(int db = -1, object? asyncState = null) -> StackExchange.Redis.IDatabase!
StackExchange.Redis.ConnectionMultiplexer.GetEndPoints(bool configuredOnly = false) -> System.Net.EndPoint![]!
StackExchange.Redis.ConnectionMultiplexer.GetHashSlot(StackExchange.Redis.RedisKey key) -> int
StackExchange.Redis.ConnectionMultiplexer.GetSentinelMasterConnection(StackExchange.Redis.ConfigurationOptions! config, System.IO.TextWriter? log = null) -> StackExchange.Redis.ConnectionMultiplexer!
StackExchange.Redis.ConnectionMultiplexer.GetServer(string! host, int port, object? asyncState = null) -> StackExchange.Redis.IServer!
StackExchange.Redis.ConnectionMultiplexer.GetServer(string! hostAndPort, object? asyncState = null) -> StackExchange.Redis.IServer!
StackExchange.Redis.ConnectionMultiplexer.GetServer(System.Net.EndPoint? endpoint, object? asyncState = null) -> StackExchange.Redis.IServer!
StackExchange.Redis.ConnectionMultiplexer.GetServer(System.Net.IPAddress! host, int port) -> StackExchange.Redis.IServer!
StackExchange.Redis.ConnectionMultiplexer.GetServers() -> StackExchange.Redis.IServer![]!
StackExchange.Redis.ConnectionMultiplexer.GetStatus() -> string!
StackExchange.Redis.ConnectionMultiplexer.GetStatus(System.IO.TextWriter! log) -> void
StackExchange.Redis.ConnectionMultiplexer.GetStormLog() -> string?
StackExchange.Redis.ConnectionMultiplexer.GetSubscriber(object? asyncState = null) -> StackExchange.Redis.ISubscriber!
StackExchange.Redis.ConnectionMultiplexer.HashSlot(StackExchange.Redis.RedisKey key) -> int
StackExchange.Redis.ConnectionMultiplexer.HashSlotMoved -> System.EventHandler<StackExchange.Redis.HashSlotMovedEventArgs!>?
StackExchange.Redis.ConnectionMultiplexer.IncludeDetailInExceptions.get -> bool
StackExchange.Redis.ConnectionMultiplexer.IncludeDetailInExceptions.set -> void
StackExchange.Redis.ConnectionMultiplexer.IncludePerformanceCountersInExceptions.get -> bool
StackExchange.Redis.ConnectionMultiplexer.IncludePerformanceCountersInExceptions.set -> void
StackExchange.Redis.ConnectionMultiplexer.InternalError -> System.EventHandler<StackExchange.Redis.InternalErrorEventArgs!>?
StackExchange.Redis.ConnectionMultiplexer.IsConnected.get -> bool
StackExchange.Redis.ConnectionMultiplexer.IsConnecting.get -> bool
StackExchange.Redis.ConnectionMultiplexer.OperationCount.get -> long
StackExchange.Redis.ConnectionMultiplexer.PreserveAsyncOrder.get -> bool
StackExchange.Redis.ConnectionMultiplexer.PreserveAsyncOrder.set -> void
StackExchange.Redis.ConnectionMultiplexer.PublishReconfigure(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.ConnectionMultiplexer.PublishReconfigureAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.ConnectionMultiplexer.ReconfigureAsync(string! reason) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.ConnectionMultiplexer.RegisterProfiler(System.Func<StackExchange.Redis.Profiling.ProfilingSession?>! profilingSessionProvider) -> void
StackExchange.Redis.ConnectionMultiplexer.ResetStormLog() -> void
StackExchange.Redis.ConnectionMultiplexer.ServerMaintenanceEvent -> System.EventHandler<StackExchange.Redis.Maintenance.ServerMaintenanceEvent!>?
StackExchange.Redis.ConnectionMultiplexer.StormLogThreshold.get -> int
StackExchange.Redis.ConnectionMultiplexer.StormLogThreshold.set -> void
StackExchange.Redis.ConnectionMultiplexer.TimeoutMilliseconds.get -> int
StackExchange.Redis.ConnectionMultiplexer.Wait(System.Threading.Tasks.Task! task) -> void
StackExchange.Redis.ConnectionMultiplexer.Wait<T>(System.Threading.Tasks.Task<T>! task) -> T
StackExchange.Redis.ConnectionMultiplexer.WaitAll(params System.Threading.Tasks.Task![]! tasks) -> void
StackExchange.Redis.ConnectionType
StackExchange.Redis.ConnectionType.Interactive = 1 -> StackExchange.Redis.ConnectionType
StackExchange.Redis.ConnectionType.None = 0 -> StackExchange.Redis.ConnectionType
StackExchange.Redis.ConnectionType.Subscription = 2 -> StackExchange.Redis.ConnectionType
StackExchange.Redis.EndPointCollection
StackExchange.Redis.EndPointCollection.Add(string! host, int port) -> void
StackExchange.Redis.EndPointCollection.Add(string! hostAndPort) -> void
StackExchange.Redis.EndPointCollection.Add(System.Net.IPAddress! host, int port) -> void
StackExchange.Redis.EndPointCollection.EndPointCollection() -> void
StackExchange.Redis.EndPointCollection.EndPointCollection(System.Collections.Generic.IList<System.Net.EndPoint!>! endpoints) -> void
StackExchange.Redis.EndPointCollection.GetEnumerator() -> System.Collections.Generic.IEnumerator<System.Net.EndPoint!>!
StackExchange.Redis.EndPointCollection.TryAdd(System.Net.EndPoint! endpoint) -> bool
StackExchange.Redis.EndPointEventArgs
StackExchange.Redis.EndPointEventArgs.EndPoint.get -> System.Net.EndPoint!
StackExchange.Redis.EndPointEventArgs.EndPointEventArgs(object! sender, System.Net.EndPoint! endpoint) -> void
StackExchange.Redis.Exclude
StackExchange.Redis.Exclude.Both = StackExchange.Redis.Exclude.Start | StackExchange.Redis.Exclude.Stop -> StackExchange.Redis.Exclude
StackExchange.Redis.Exclude.None = 0 -> StackExchange.Redis.Exclude
StackExchange.Redis.Exclude.Start = 1 -> StackExchange.Redis.Exclude
StackExchange.Redis.Exclude.Stop = 2 -> StackExchange.Redis.Exclude
StackExchange.Redis.ExpireResult
StackExchange.Redis.ExpireResult.ConditionNotMet = 0 -> StackExchange.Redis.ExpireResult
StackExchange.Redis.ExpireResult.Due = 2 -> StackExchange.Redis.ExpireResult
StackExchange.Redis.ExpireResult.NoSuchField = -2 -> StackExchange.Redis.ExpireResult
StackExchange.Redis.ExpireResult.Success = 1 -> StackExchange.Redis.ExpireResult
StackExchange.Redis.ExpireWhen
StackExchange.Redis.ExpireWhen.Always = 0 -> StackExchange.Redis.ExpireWhen
StackExchange.Redis.ExpireWhen.GreaterThanCurrentExpiry = 1 -> StackExchange.Redis.ExpireWhen
StackExchange.Redis.ExpireWhen.HasExpiry = 2 -> StackExchange.Redis.ExpireWhen
StackExchange.Redis.ExpireWhen.HasNoExpiry = 3 -> StackExchange.Redis.ExpireWhen
StackExchange.Redis.ExpireWhen.LessThanCurrentExpiry = 4 -> StackExchange.Redis.ExpireWhen
StackExchange.Redis.ExponentialRetry
StackExchange.Redis.ExponentialRetry.ExponentialRetry(int deltaBackOffMilliseconds) -> void
StackExchange.Redis.ExponentialRetry.ExponentialRetry(int deltaBackOffMilliseconds, int maxDeltaBackOffMilliseconds) -> void
StackExchange.Redis.ExponentialRetry.ShouldRetry(long currentRetryCount, int timeElapsedMillisecondsSinceLastRetry) -> bool
StackExchange.Redis.ExportOptions
StackExchange.Redis.ExportOptions.All = -1 -> StackExchange.Redis.ExportOptions
StackExchange.Redis.ExportOptions.Client = 4 -> StackExchange.Redis.ExportOptions
StackExchange.Redis.ExportOptions.Cluster = 8 -> StackExchange.Redis.ExportOptions
StackExchange.Redis.ExportOptions.Config = 2 -> StackExchange.Redis.ExportOptions
StackExchange.Redis.ExportOptions.Info = 1 -> StackExchange.Redis.ExportOptions
StackExchange.Redis.ExportOptions.None = 0 -> StackExchange.Redis.ExportOptions
StackExchange.Redis.ExtensionMethods
StackExchange.Redis.GeoEntry
StackExchange.Redis.GeoEntry.Equals(StackExchange.Redis.GeoEntry other) -> bool
StackExchange.Redis.GeoEntry.GeoEntry() -> void
StackExchange.Redis.GeoEntry.GeoEntry(double longitude, double latitude, StackExchange.Redis.RedisValue member) -> void
StackExchange.Redis.GeoEntry.Latitude.get -> double
StackExchange.Redis.GeoEntry.Longitude.get -> double
StackExchange.Redis.GeoEntry.Member.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.GeoEntry.Position.get -> StackExchange.Redis.GeoPosition
StackExchange.Redis.GeoPosition
StackExchange.Redis.GeoPosition.Equals(StackExchange.Redis.GeoPosition other) -> bool
StackExchange.Redis.GeoPosition.GeoPosition() -> void
StackExchange.Redis.GeoPosition.GeoPosition(double longitude, double latitude) -> void
StackExchange.Redis.GeoPosition.Latitude.get -> double
StackExchange.Redis.GeoPosition.Longitude.get -> double
StackExchange.Redis.GeoRadiusOptions
StackExchange.Redis.GeoRadiusOptions.Default = StackExchange.Redis.GeoRadiusOptions.WithCoordinates | StackExchange.Redis.GeoRadiusOptions.WithDistance -> StackExchange.Redis.GeoRadiusOptions
StackExchange.Redis.GeoRadiusOptions.None = 0 -> StackExchange.Redis.GeoRadiusOptions
StackExchange.Redis.GeoRadiusOptions.WithCoordinates = 1 -> StackExchange.Redis.GeoRadiusOptions
StackExchange.Redis.GeoRadiusOptions.WithDistance = 2 -> StackExchange.Redis.GeoRadiusOptions
StackExchange.Redis.GeoRadiusOptions.WithGeoHash = 4 -> StackExchange.Redis.GeoRadiusOptions
StackExchange.Redis.GeoRadiusResult
StackExchange.Redis.GeoRadiusResult.Distance.get -> double?
StackExchange.Redis.GeoRadiusResult.GeoRadiusResult() -> void
StackExchange.Redis.GeoRadiusResult.GeoRadiusResult(in StackExchange.Redis.RedisValue member, double? distance, long? hash, StackExchange.Redis.GeoPosition? position) -> void
StackExchange.Redis.GeoRadiusResult.Hash.get -> long?
StackExchange.Redis.GeoRadiusResult.Member.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.GeoRadiusResult.Position.get -> StackExchange.Redis.GeoPosition?
StackExchange.Redis.GeoSearchBox
StackExchange.Redis.GeoSearchBox.GeoSearchBox(double height, double width, StackExchange.Redis.GeoUnit unit = StackExchange.Redis.GeoUnit.Meters) -> void
StackExchange.Redis.GeoSearchCircle
StackExchange.Redis.GeoSearchCircle.GeoSearchCircle(double radius, StackExchange.Redis.GeoUnit unit = StackExchange.Redis.GeoUnit.Meters) -> void
StackExchange.Redis.GeoSearchShape
StackExchange.Redis.GeoSearchShape.GeoSearchShape(StackExchange.Redis.GeoUnit unit) -> void
StackExchange.Redis.GeoSearchShape.Unit.get -> StackExchange.Redis.GeoUnit
StackExchange.Redis.GeoUnit
StackExchange.Redis.GeoUnit.Feet = 3 -> StackExchange.Redis.GeoUnit
StackExchange.Redis.GeoUnit.Kilometers = 1 -> StackExchange.Redis.GeoUnit
StackExchange.Redis.GeoUnit.Meters = 0 -> StackExchange.Redis.GeoUnit
StackExchange.Redis.GeoUnit.Miles = 2 -> StackExchange.Redis.GeoUnit
StackExchange.Redis.HashEntry
StackExchange.Redis.HashEntry.Equals(StackExchange.Redis.HashEntry other) -> bool
StackExchange.Redis.HashEntry.HashEntry() -> void
StackExchange.Redis.HashEntry.HashEntry(StackExchange.Redis.RedisValue name, StackExchange.Redis.RedisValue value) -> void
StackExchange.Redis.HashEntry.Key.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.HashEntry.Name.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.HashEntry.Value.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.HashSlotMovedEventArgs
StackExchange.Redis.HashSlotMovedEventArgs.HashSlot.get -> int
StackExchange.Redis.HashSlotMovedEventArgs.HashSlotMovedEventArgs(object! sender, int hashSlot, System.Net.EndPoint! old, System.Net.EndPoint! new) -> void
StackExchange.Redis.HashSlotMovedEventArgs.NewEndPoint.get -> System.Net.EndPoint!
StackExchange.Redis.HashSlotMovedEventArgs.OldEndPoint.get -> System.Net.EndPoint?
StackExchange.Redis.IBatch
StackExchange.Redis.IBatch.Execute() -> void
StackExchange.Redis.IConnectionMultiplexer
StackExchange.Redis.IConnectionMultiplexer.ClientName.get -> string!
StackExchange.Redis.IConnectionMultiplexer.Close(bool allowCommandsToComplete = true) -> void
StackExchange.Redis.IConnectionMultiplexer.CloseAsync(bool allowCommandsToComplete = true) -> System.Threading.Tasks.Task!
StackExchange.Redis.IConnectionMultiplexer.Configuration.get -> string!
StackExchange.Redis.IConnectionMultiplexer.ConfigurationChanged -> System.EventHandler<StackExchange.Redis.EndPointEventArgs!>!
StackExchange.Redis.IConnectionMultiplexer.ConfigurationChangedBroadcast -> System.EventHandler<StackExchange.Redis.EndPointEventArgs!>!
StackExchange.Redis.IConnectionMultiplexer.Configure(System.IO.TextWriter? log = null) -> bool
StackExchange.Redis.IConnectionMultiplexer.ConfigureAsync(System.IO.TextWriter? log = null) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IConnectionMultiplexer.ConnectionFailed -> System.EventHandler<StackExchange.Redis.ConnectionFailedEventArgs!>!
StackExchange.Redis.IConnectionMultiplexer.ConnectionRestored -> System.EventHandler<StackExchange.Redis.ConnectionFailedEventArgs!>!
StackExchange.Redis.IConnectionMultiplexer.ErrorMessage -> System.EventHandler<StackExchange.Redis.RedisErrorEventArgs!>!
StackExchange.Redis.IConnectionMultiplexer.ExportConfiguration(System.IO.Stream! destination, StackExchange.Redis.ExportOptions options = (StackExchange.Redis.ExportOptions)-1) -> void
StackExchange.Redis.IConnectionMultiplexer.GetCounters() -> StackExchange.Redis.ServerCounters!
StackExchange.Redis.IConnectionMultiplexer.GetDatabase(int db = -1, object? asyncState = null) -> StackExchange.Redis.IDatabase!
StackExchange.Redis.IConnectionMultiplexer.GetEndPoints(bool configuredOnly = false) -> System.Net.EndPoint![]!
StackExchange.Redis.IConnectionMultiplexer.GetHashSlot(StackExchange.Redis.RedisKey key) -> int
StackExchange.Redis.IConnectionMultiplexer.GetServer(string! host, int port, object? asyncState = null) -> StackExchange.Redis.IServer!
StackExchange.Redis.IConnectionMultiplexer.GetServer(string! hostAndPort, object? asyncState = null) -> StackExchange.Redis.IServer!
StackExchange.Redis.IConnectionMultiplexer.GetServer(System.Net.EndPoint! endpoint, object? asyncState = null) -> StackExchange.Redis.IServer!
StackExchange.Redis.IConnectionMultiplexer.GetServer(System.Net.IPAddress! host, int port) -> StackExchange.Redis.IServer!
StackExchange.Redis.IConnectionMultiplexer.GetServers() -> StackExchange.Redis.IServer![]!
StackExchange.Redis.IConnectionMultiplexer.GetStatus() -> string!
StackExchange.Redis.IConnectionMultiplexer.GetStatus(System.IO.TextWriter! log) -> void
StackExchange.Redis.IConnectionMultiplexer.GetStormLog() -> string?
StackExchange.Redis.IConnectionMultiplexer.GetSubscriber(object? asyncState = null) -> StackExchange.Redis.ISubscriber!
StackExchange.Redis.IConnectionMultiplexer.HashSlot(StackExchange.Redis.RedisKey key) -> int
StackExchange.Redis.IConnectionMultiplexer.HashSlotMoved -> System.EventHandler<StackExchange.Redis.HashSlotMovedEventArgs!>!
StackExchange.Redis.IConnectionMultiplexer.IncludeDetailInExceptions.get -> bool
StackExchange.Redis.IConnectionMultiplexer.IncludeDetailInExceptions.set -> void
StackExchange.Redis.IConnectionMultiplexer.InternalError -> System.EventHandler<StackExchange.Redis.InternalErrorEventArgs!>!
StackExchange.Redis.IConnectionMultiplexer.IsConnected.get -> bool
StackExchange.Redis.IConnectionMultiplexer.IsConnecting.get -> bool
StackExchange.Redis.IConnectionMultiplexer.OperationCount.get -> long
StackExchange.Redis.IConnectionMultiplexer.PreserveAsyncOrder.get -> bool
StackExchange.Redis.IConnectionMultiplexer.PreserveAsyncOrder.set -> void
StackExchange.Redis.IConnectionMultiplexer.PublishReconfigure(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IConnectionMultiplexer.PublishReconfigureAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IConnectionMultiplexer.RegisterProfiler(System.Func<StackExchange.Redis.Profiling.ProfilingSession?>! profilingSessionProvider) -> void
StackExchange.Redis.IConnectionMultiplexer.ResetStormLog() -> void
StackExchange.Redis.IConnectionMultiplexer.ServerMaintenanceEvent -> System.EventHandler<StackExchange.Redis.Maintenance.ServerMaintenanceEvent!>!
StackExchange.Redis.IConnectionMultiplexer.StormLogThreshold.get -> int
StackExchange.Redis.IConnectionMultiplexer.StormLogThreshold.set -> void
StackExchange.Redis.IConnectionMultiplexer.TimeoutMilliseconds.get -> int
StackExchange.Redis.IConnectionMultiplexer.ToString() -> string!
StackExchange.Redis.IConnectionMultiplexer.Wait(System.Threading.Tasks.Task! task) -> void
StackExchange.Redis.IConnectionMultiplexer.Wait<T>(System.Threading.Tasks.Task<T>! task) -> T
StackExchange.Redis.IConnectionMultiplexer.WaitAll(params System.Threading.Tasks.Task![]! tasks) -> void
StackExchange.Redis.IDatabase
StackExchange.Redis.IDatabase.CreateBatch(object? asyncState = null) -> StackExchange.Redis.IBatch!
StackExchange.Redis.IDatabase.CreateTransaction(object? asyncState = null) -> StackExchange.Redis.ITransaction!
StackExchange.Redis.IDatabase.Database.get -> int
StackExchange.Redis.IDatabase.DebugObject(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.Execute(string! command, params object![]! args) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IDatabase.Execute(string! command, System.Collections.Generic.ICollection<object!>! args, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IDatabase.GeoAdd(StackExchange.Redis.RedisKey key, double longitude, double latitude, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.GeoAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.GeoEntry value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.GeoAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.GeoEntry[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.GeoDistance(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member1, StackExchange.Redis.RedisValue member2, StackExchange.Redis.GeoUnit unit = StackExchange.Redis.GeoUnit.Meters, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double?
StackExchange.Redis.IDatabase.GeoHash(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string?
StackExchange.Redis.IDatabase.GeoHash(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! members, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string?[]!
StackExchange.Redis.IDatabase.GeoPosition(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.GeoPosition?
StackExchange.Redis.IDatabase.GeoPosition(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! members, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.GeoPosition?[]!
StackExchange.Redis.IDatabase.GeoRadius(StackExchange.Redis.RedisKey key, double longitude, double latitude, double radius, StackExchange.Redis.GeoUnit unit = StackExchange.Redis.GeoUnit.Meters, int count = -1, StackExchange.Redis.Order? order = null, StackExchange.Redis.GeoRadiusOptions options = StackExchange.Redis.GeoRadiusOptions.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.GeoRadiusResult[]!
StackExchange.Redis.IDatabase.GeoRadius(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double radius, StackExchange.Redis.GeoUnit unit = StackExchange.Redis.GeoUnit.Meters, int count = -1, StackExchange.Redis.Order? order = null, StackExchange.Redis.GeoRadiusOptions options = StackExchange.Redis.GeoRadiusOptions.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.GeoRadiusResult[]!
StackExchange.Redis.IDatabase.GeoRemove(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.GeoSearch(StackExchange.Redis.RedisKey key, double longitude, double latitude, StackExchange.Redis.GeoSearchShape! shape, int count = -1, bool demandClosest = true, StackExchange.Redis.Order? order = null, StackExchange.Redis.GeoRadiusOptions options = StackExchange.Redis.GeoRadiusOptions.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.GeoRadiusResult[]!
StackExchange.Redis.IDatabase.GeoSearch(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.GeoSearchShape! shape, int count = -1, bool demandClosest = true, StackExchange.Redis.Order? order = null, StackExchange.Redis.GeoRadiusOptions options = StackExchange.Redis.GeoRadiusOptions.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.GeoRadiusResult[]!
StackExchange.Redis.IDatabase.GeoSearchAndStore(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, double longitude, double latitude, StackExchange.Redis.GeoSearchShape! shape, int count = -1, bool demandClosest = true, StackExchange.Redis.Order? order = null, bool storeDistances = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.GeoSearchAndStore(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, StackExchange.Redis.RedisValue member, StackExchange.Redis.GeoSearchShape! shape, int count = -1, bool demandClosest = true, StackExchange.Redis.Order? order = null, bool storeDistances = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.HashDecrement(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double
StackExchange.Redis.IDatabase.HashDecrement(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, long value = 1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.HashDelete(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.HashDelete(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.HashExists(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.HashFieldExpire(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, System.DateTime expiry, StackExchange.Redis.ExpireWhen when = StackExchange.Redis.ExpireWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.ExpireResult[]!
StackExchange.Redis.IDatabase.HashFieldExpire(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, System.TimeSpan expiry, StackExchange.Redis.ExpireWhen when = StackExchange.Redis.ExpireWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.ExpireResult[]!
StackExchange.Redis.IDatabase.HashFieldGetExpireDateTime(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long[]!
StackExchange.Redis.IDatabase.HashFieldGetTimeToLive(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long[]!
StackExchange.Redis.IDatabase.HashFieldPersist(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.PersistResult[]!
StackExchange.Redis.IDatabase.HashGet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.HashGet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.HashGetAll(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.HashEntry[]!
StackExchange.Redis.IDatabase.HashGetLease(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.Lease<byte>?
StackExchange.Redis.IDatabase.HashIncrement(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double
StackExchange.Redis.IDatabase.HashIncrement(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, long value = 1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.HashKeys(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.HashLength(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.HashRandomField(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.HashRandomFields(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.HashRandomFieldsWithValues(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.HashEntry[]!
StackExchange.Redis.IDatabase.HashScan(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.HashEntry>!
StackExchange.Redis.IDatabase.HashScan(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern, int pageSize, StackExchange.Redis.CommandFlags flags) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.HashEntry>!
StackExchange.Redis.IDatabase.HashScanNoValues(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabase.HashSet(StackExchange.Redis.RedisKey key, StackExchange.Redis.HashEntry[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IDatabase.HashSet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.RedisValue value, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.HashStringLength(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.HashValues(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.HyperLogLogAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.HyperLogLogAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.HyperLogLogLength(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.HyperLogLogLength(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.HyperLogLogMerge(StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IDatabase.HyperLogLogMerge(StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey[]! sourceKeys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IDatabase.IdentifyEndpoint(StackExchange.Redis.RedisKey key = default(StackExchange.Redis.RedisKey), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Net.EndPoint?
StackExchange.Redis.IDatabase.KeyCopy(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, int destinationDatabase = -1, bool replace = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyDelete(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyDelete(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.KeyDump(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> byte[]?
StackExchange.Redis.IDatabase.KeyEncoding(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string?
StackExchange.Redis.IDatabase.KeyExists(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyExists(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.KeyExpire(StackExchange.Redis.RedisKey key, System.DateTime? expiry, StackExchange.Redis.CommandFlags flags) -> bool
StackExchange.Redis.IDatabase.KeyExpire(StackExchange.Redis.RedisKey key, System.DateTime? expiry, StackExchange.Redis.ExpireWhen when = StackExchange.Redis.ExpireWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyExpire(StackExchange.Redis.RedisKey key, System.TimeSpan? expiry, StackExchange.Redis.CommandFlags flags) -> bool
StackExchange.Redis.IDatabase.KeyExpire(StackExchange.Redis.RedisKey key, System.TimeSpan? expiry, StackExchange.Redis.ExpireWhen when = StackExchange.Redis.ExpireWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyExpireTime(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.DateTime?
StackExchange.Redis.IDatabase.KeyFrequency(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long?
StackExchange.Redis.IDatabase.KeyIdleTime(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.TimeSpan?
StackExchange.Redis.IDatabase.KeyMigrate(StackExchange.Redis.RedisKey key, System.Net.EndPoint! toServer, int toDatabase = 0, int timeoutMilliseconds = 0, StackExchange.Redis.MigrateOptions migrateOptions = StackExchange.Redis.MigrateOptions.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IDatabase.KeyMove(StackExchange.Redis.RedisKey key, int database, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyPersist(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyRandom(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisKey
StackExchange.Redis.IDatabase.KeyRefCount(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long?
StackExchange.Redis.IDatabase.KeyRename(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisKey newKey, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyRestore(StackExchange.Redis.RedisKey key, byte[]! value, System.TimeSpan? expiry = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IDatabase.KeyTimeToLive(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.TimeSpan?
StackExchange.Redis.IDatabase.KeyTouch(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.KeyTouch(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.KeyType(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisType
StackExchange.Redis.IDatabase.ListGetByIndex(StackExchange.Redis.RedisKey key, long index, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.ListInsertAfter(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pivot, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListInsertBefore(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pivot, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListMove(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, StackExchange.Redis.ListSide sourceSide, StackExchange.Redis.ListSide destinationSide, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.ListLeftPop(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.ListLeftPop(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.ListLeftPop(StackExchange.Redis.RedisKey[]! keys, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.ListPopResult
StackExchange.Redis.IDatabase.ListLeftPush(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListLeftPush(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags) -> long
StackExchange.Redis.IDatabase.ListLeftPush(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListLength(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListPosition(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue element, long rank = 1, long maxLength = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListPositions(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue element, long count, long rank = 1, long maxLength = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long[]!
StackExchange.Redis.IDatabase.ListRange(StackExchange.Redis.RedisKey key, long start = 0, long stop = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.ListRemove(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, long count = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListRightPop(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.ListRightPop(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.ListRightPop(StackExchange.Redis.RedisKey[]! keys, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.ListPopResult
StackExchange.Redis.IDatabase.ListRightPopLeftPush(StackExchange.Redis.RedisKey source, StackExchange.Redis.RedisKey destination, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.ListRightPush(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListRightPush(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags) -> long
StackExchange.Redis.IDatabase.ListRightPush(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ListSetByIndex(StackExchange.Redis.RedisKey key, long index, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IDatabase.ListTrim(StackExchange.Redis.RedisKey key, long start, long stop, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IDatabase.LockExtend(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan expiry, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.LockQuery(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.LockRelease(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.LockTake(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan expiry, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.Publish(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.RedisValue message, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.ScriptEvaluate(byte[]! hash, StackExchange.Redis.RedisKey[]? keys = null, StackExchange.Redis.RedisValue[]? values = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IDatabase.ScriptEvaluate(StackExchange.Redis.LoadedLuaScript! script, object? parameters = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IDatabase.ScriptEvaluate(StackExchange.Redis.LuaScript! script, object? parameters = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IDatabase.ScriptEvaluate(string! script, StackExchange.Redis.RedisKey[]? keys = null, StackExchange.Redis.RedisValue[]? values = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IDatabase.ScriptEvaluateReadOnly(byte[]! hash, StackExchange.Redis.RedisKey[]? keys = null, StackExchange.Redis.RedisValue[]? values = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IDatabase.ScriptEvaluateReadOnly(string! script, StackExchange.Redis.RedisKey[]? keys = null, StackExchange.Redis.RedisValue[]? values = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IDatabase.SetAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.SetAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SetCombine(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SetCombine(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SetCombineAndStore(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SetCombineAndStore(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SetContains(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.SetContains(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool[]!
StackExchange.Redis.IDatabase.SetIntersectionLength(StackExchange.Redis.RedisKey[]! keys, long limit = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SetLength(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SetMembers(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SetMove(StackExchange.Redis.RedisKey source, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.SetPop(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SetPop(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.SetRandomMember(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.SetRandomMembers(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SetRemove(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.SetRemove(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SetScan(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabase.SetScan(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern, int pageSize, StackExchange.Redis.CommandFlags flags) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabase.Sort(StackExchange.Redis.RedisKey key, long skip = 0, long take = -1, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.SortType sortType = StackExchange.Redis.SortType.Numeric, StackExchange.Redis.RedisValue by = default(StackExchange.Redis.RedisValue), StackExchange.Redis.RedisValue[]? get = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SortAndStore(StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey key, long skip = 0, long take = -1, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.SortType sortType = StackExchange.Redis.SortType.Numeric, StackExchange.Redis.RedisValue by = default(StackExchange.Redis.RedisValue), StackExchange.Redis.RedisValue[]? get = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double score, StackExchange.Redis.CommandFlags flags) -> bool
StackExchange.Redis.IDatabase.SortedSetAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double score, StackExchange.Redis.When when, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.SortedSetAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double score, StackExchange.Redis.SortedSetWhen when = StackExchange.Redis.SortedSetWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.SortedSetAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.SortedSetEntry[]! values, StackExchange.Redis.CommandFlags flags) -> long
StackExchange.Redis.IDatabase.SortedSetAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.SortedSetEntry[]! values, StackExchange.Redis.When when, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.SortedSetEntry[]! values, StackExchange.Redis.SortedSetWhen when = StackExchange.Redis.SortedSetWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetCombine(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey[]! keys, double[]? weights = null, StackExchange.Redis.Aggregate aggregate = StackExchange.Redis.Aggregate.Sum, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SortedSetCombineWithScores(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey[]! keys, double[]? weights = null, StackExchange.Redis.Aggregate aggregate = StackExchange.Redis.Aggregate.Sum, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.SortedSetEntry[]!
StackExchange.Redis.IDatabase.SortedSetCombineAndStore(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.Aggregate aggregate = StackExchange.Redis.Aggregate.Sum, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetCombineAndStore(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey[]! keys, double[]? weights = null, StackExchange.Redis.Aggregate aggregate = StackExchange.Redis.Aggregate.Sum, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetDecrement(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double
StackExchange.Redis.IDatabase.SortedSetIncrement(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double
StackExchange.Redis.IDatabase.SortedSetIntersectionLength(StackExchange.Redis.RedisKey[]! keys, long limit = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetLength(StackExchange.Redis.RedisKey key, double min = -Infinity, double max = Infinity, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetLengthByValue(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue min, StackExchange.Redis.RedisValue max, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetPop(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.SortedSetEntry[]!
StackExchange.Redis.IDatabase.SortedSetPop(StackExchange.Redis.RedisKey key, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.SortedSetEntry?
StackExchange.Redis.IDatabase.SortedSetPop(StackExchange.Redis.RedisKey[]! keys, long count, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.SortedSetPopResult
StackExchange.Redis.IDatabase.SortedSetRandomMember(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.SortedSetRandomMembers(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SortedSetRandomMembersWithScores(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.SortedSetEntry[]!
StackExchange.Redis.IDatabase.SortedSetRangeByRank(StackExchange.Redis.RedisKey key, long start = 0, long stop = -1, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SortedSetRangeAndStore(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, StackExchange.Redis.RedisValue start, StackExchange.Redis.RedisValue stop, StackExchange.Redis.SortedSetOrder sortedSetOrder = StackExchange.Redis.SortedSetOrder.ByRank, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, long skip = 0, long? take = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetRangeByRankWithScores(StackExchange.Redis.RedisKey key, long start = 0, long stop = -1, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.SortedSetEntry[]!
StackExchange.Redis.IDatabase.SortedSetRangeByScore(StackExchange.Redis.RedisKey key, double start = -Infinity, double stop = Infinity, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, long skip = 0, long take = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SortedSetRangeByScoreWithScores(StackExchange.Redis.RedisKey key, double start = -Infinity, double stop = Infinity, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, long skip = 0, long take = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.SortedSetEntry[]!
StackExchange.Redis.IDatabase.SortedSetRangeByValue(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue min = default(StackExchange.Redis.RedisValue), StackExchange.Redis.RedisValue max = default(StackExchange.Redis.RedisValue), StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, long skip = 0, long take = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SortedSetRangeByValue(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue min, StackExchange.Redis.RedisValue max, StackExchange.Redis.Exclude exclude, long skip, long take = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.SortedSetRank(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long?
StackExchange.Redis.IDatabase.SortedSetRemove(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.SortedSetRemove(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! members, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetRemoveRangeByRank(StackExchange.Redis.RedisKey key, long start, long stop, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetRemoveRangeByScore(StackExchange.Redis.RedisKey key, double start, double stop, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetRemoveRangeByValue(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue min, StackExchange.Redis.RedisValue max, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.SortedSetScan(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.SortedSetEntry>!
StackExchange.Redis.IDatabase.SortedSetScan(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern, int pageSize, StackExchange.Redis.CommandFlags flags) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.SortedSetEntry>!
StackExchange.Redis.IDatabase.SortedSetScore(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double?
StackExchange.Redis.IDatabase.SortedSetScores(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! members, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double?[]!
StackExchange.Redis.IDatabase.SortedSetUpdate(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double score, StackExchange.Redis.SortedSetWhen when = StackExchange.Redis.SortedSetWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.SortedSetUpdate(StackExchange.Redis.RedisKey key, StackExchange.Redis.SortedSetEntry[]! values, StackExchange.Redis.SortedSetWhen when = StackExchange.Redis.SortedSetWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StreamAcknowledge(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue messageId, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StreamAcknowledge(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue[]! messageIds, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StreamAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.NameValueEntry[]! streamPairs, StackExchange.Redis.RedisValue? messageId = null, int? maxLength = null, bool useApproximateMaxLength = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StreamAdd(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue streamField, StackExchange.Redis.RedisValue streamValue, StackExchange.Redis.RedisValue? messageId = null, int? maxLength = null, bool useApproximateMaxLength = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StreamAutoClaim(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue consumerGroup, StackExchange.Redis.RedisValue claimingConsumer, long minIdleTimeInMs, StackExchange.Redis.RedisValue startAtId, int? count = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamAutoClaimResult
StackExchange.Redis.IDatabase.StreamAutoClaimIdsOnly(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue consumerGroup, StackExchange.Redis.RedisValue claimingConsumer, long minIdleTimeInMs, StackExchange.Redis.RedisValue startAtId, int? count = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamAutoClaimIdsOnlyResult
StackExchange.Redis.IDatabase.StreamClaim(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue consumerGroup, StackExchange.Redis.RedisValue claimingConsumer, long minIdleTimeInMs, StackExchange.Redis.RedisValue[]! messageIds, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamEntry[]!
StackExchange.Redis.IDatabase.StreamClaimIdsOnly(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue consumerGroup, StackExchange.Redis.RedisValue claimingConsumer, long minIdleTimeInMs, StackExchange.Redis.RedisValue[]! messageIds, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.StreamConsumerGroupSetPosition(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue position, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.StreamConsumerInfo(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamConsumerInfo[]!
StackExchange.Redis.IDatabase.StreamCreateConsumerGroup(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue? position = null, bool createStream = true, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.StreamCreateConsumerGroup(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue? position, StackExchange.Redis.CommandFlags flags) -> bool
StackExchange.Redis.IDatabase.StreamDelete(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! messageIds, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StreamDeleteConsumer(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StreamDeleteConsumerGroup(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.StreamGroupInfo(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamGroupInfo[]!
StackExchange.Redis.IDatabase.StreamInfo(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamInfo
StackExchange.Redis.IDatabase.StreamLength(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StreamPending(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamPendingInfo
StackExchange.Redis.IDatabase.StreamPendingMessages(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, int count, StackExchange.Redis.RedisValue consumerName, StackExchange.Redis.RedisValue? minId = null, StackExchange.Redis.RedisValue? maxId = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamPendingMessageInfo[]!
StackExchange.Redis.IDatabase.StreamRange(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue? minId = null, StackExchange.Redis.RedisValue? maxId = null, int? count = null, StackExchange.Redis.Order messageOrder = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamEntry[]!
StackExchange.Redis.IDatabase.StreamRead(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue position, int? count = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamEntry[]!
StackExchange.Redis.IDatabase.StreamRead(StackExchange.Redis.StreamPosition[]! streamPositions, int? countPerStream = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisStream[]!
StackExchange.Redis.IDatabase.StreamReadGroup(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, StackExchange.Redis.RedisValue? position = null, int? count = null, bool noAck = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.StreamEntry[]!
StackExchange.Redis.IDatabase.StreamReadGroup(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, StackExchange.Redis.RedisValue? position, int? count, StackExchange.Redis.CommandFlags flags) -> StackExchange.Redis.StreamEntry[]!
StackExchange.Redis.IDatabase.StreamReadGroup(StackExchange.Redis.StreamPosition[]! streamPositions, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, int? countPerStream = null, bool noAck = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisStream[]!
StackExchange.Redis.IDatabase.StreamReadGroup(StackExchange.Redis.StreamPosition[]! streamPositions, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, int? countPerStream, StackExchange.Redis.CommandFlags flags) -> StackExchange.Redis.RedisStream[]!
StackExchange.Redis.IDatabase.StreamTrim(StackExchange.Redis.RedisKey key, int maxLength, bool useApproximateMaxLength = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringAppend(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringBitCount(StackExchange.Redis.RedisKey key, long start, long end, StackExchange.Redis.CommandFlags flags) -> long
StackExchange.Redis.IDatabase.StringBitCount(StackExchange.Redis.RedisKey key, long start = 0, long end = -1, StackExchange.Redis.StringIndexType indexType = StackExchange.Redis.StringIndexType.Byte, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringBitOperation(StackExchange.Redis.Bitwise operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second = default(StackExchange.Redis.RedisKey), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringBitOperation(StackExchange.Redis.Bitwise operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringBitPosition(StackExchange.Redis.RedisKey key, bool bit, long start, long end, StackExchange.Redis.CommandFlags flags) -> long
StackExchange.Redis.IDatabase.StringBitPosition(StackExchange.Redis.RedisKey key, bool bit, long start = 0, long end = -1, StackExchange.Redis.StringIndexType indexType = StackExchange.Redis.StringIndexType.Byte, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringDecrement(StackExchange.Redis.RedisKey key, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double
StackExchange.Redis.IDatabase.StringDecrement(StackExchange.Redis.RedisKey key, long value = 1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringGet(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StringGet(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.IDatabase.StringGetBit(StackExchange.Redis.RedisKey key, long offset, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.StringGetDelete(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StringGetLease(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.Lease<byte>?
StackExchange.Redis.IDatabase.StringGetRange(StackExchange.Redis.RedisKey key, long start, long end, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StringGetSet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StringGetSetExpiry(StackExchange.Redis.RedisKey key, System.TimeSpan? expiry, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StringGetSetExpiry(StackExchange.Redis.RedisKey key, System.DateTime expiry, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StringGetWithExpiry(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValueWithExpiry
StackExchange.Redis.IDatabase.StringIncrement(StackExchange.Redis.RedisKey key, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> double
StackExchange.Redis.IDatabase.StringIncrement(StackExchange.Redis.RedisKey key, long value = 1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringLength(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringLongestCommonSubsequence(StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string?
StackExchange.Redis.IDatabase.StringLongestCommonSubsequenceLength(StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IDatabase.StringLongestCommonSubsequenceWithMatches(StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, long minLength = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.LCSMatchResult
StackExchange.Redis.IDatabase.StringSet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry = null, bool keepTtl = false, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.StringSet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry, StackExchange.Redis.When when) -> bool
StackExchange.Redis.IDatabase.StringSet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry, StackExchange.Redis.When when, StackExchange.Redis.CommandFlags flags) -> bool
StackExchange.Redis.IDatabase.StringSet(System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisKey, StackExchange.Redis.RedisValue>[]! values, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.StringSetAndGet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry = null, bool keepTtl = false, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StringSetAndGet(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry, StackExchange.Redis.When when, StackExchange.Redis.CommandFlags flags) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabase.StringSetBit(StackExchange.Redis.RedisKey key, long offset, bool bit, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabase.StringSetRange(StackExchange.Redis.RedisKey key, long offset, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IDatabaseAsync
StackExchange.Redis.IDatabaseAsync.DebugObjectAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.ExecuteAsync(string! command, params object![]! args) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IDatabaseAsync.ExecuteAsync(string! command, System.Collections.Generic.ICollection<object!>? args, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IDatabaseAsync.GeoAddAsync(StackExchange.Redis.RedisKey key, double longitude, double latitude, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.GeoAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.GeoEntry value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.GeoAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.GeoEntry[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.GeoDistanceAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member1, StackExchange.Redis.RedisValue member2, StackExchange.Redis.GeoUnit unit = StackExchange.Redis.GeoUnit.Meters, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double?>!
StackExchange.Redis.IDatabaseAsync.GeoHashAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string?>!
StackExchange.Redis.IDatabaseAsync.GeoHashAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! members, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string?[]!>!
StackExchange.Redis.IDatabaseAsync.GeoPositionAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.GeoPosition?>!
StackExchange.Redis.IDatabaseAsync.GeoPositionAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! members, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.GeoPosition?[]!>!
StackExchange.Redis.IDatabaseAsync.GeoRadiusAsync(StackExchange.Redis.RedisKey key, double longitude, double latitude, double radius, StackExchange.Redis.GeoUnit unit = StackExchange.Redis.GeoUnit.Meters, int count = -1, StackExchange.Redis.Order? order = null, StackExchange.Redis.GeoRadiusOptions options = StackExchange.Redis.GeoRadiusOptions.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.GeoRadiusResult[]!>!
StackExchange.Redis.IDatabaseAsync.GeoRadiusAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double radius, StackExchange.Redis.GeoUnit unit = StackExchange.Redis.GeoUnit.Meters, int count = -1, StackExchange.Redis.Order? order = null, StackExchange.Redis.GeoRadiusOptions options = StackExchange.Redis.GeoRadiusOptions.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.GeoRadiusResult[]!>!
StackExchange.Redis.IDatabaseAsync.GeoRemoveAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.GeoSearchAsync(StackExchange.Redis.RedisKey key, double longitude, double latitude, StackExchange.Redis.GeoSearchShape! shape, int count = -1, bool demandClosest = true, StackExchange.Redis.Order? order = null, StackExchange.Redis.GeoRadiusOptions options = StackExchange.Redis.GeoRadiusOptions.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.GeoRadiusResult[]!>!
StackExchange.Redis.IDatabaseAsync.GeoSearchAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.GeoSearchShape! shape, int count = -1, bool demandClosest = true, StackExchange.Redis.Order? order = null, StackExchange.Redis.GeoRadiusOptions options = StackExchange.Redis.GeoRadiusOptions.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.GeoRadiusResult[]!>!
StackExchange.Redis.IDatabaseAsync.GeoSearchAndStoreAsync(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, double longitude, double latitude, StackExchange.Redis.GeoSearchShape! shape, int count = -1, bool demandClosest = true, StackExchange.Redis.Order? order = null, bool storeDistances = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.GeoSearchAndStoreAsync(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, StackExchange.Redis.RedisValue member, StackExchange.Redis.GeoSearchShape! shape, int count = -1, bool demandClosest = true, StackExchange.Redis.Order? order = null, bool storeDistances = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.HashDecrementAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double>!
StackExchange.Redis.IDatabaseAsync.HashDecrementAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, long value = 1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.HashDeleteAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.HashDeleteAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.HashExistsAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!

StackExchange.Redis.IDatabaseAsync.HashFieldExpireAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, System.DateTime expiry, StackExchange.Redis.ExpireWhen when = StackExchange.Redis.ExpireWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.ExpireResult[]!>!
StackExchange.Redis.IDatabaseAsync.HashFieldExpireAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, System.TimeSpan expiry, StackExchange.Redis.ExpireWhen when = StackExchange.Redis.ExpireWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.ExpireResult[]!>!
StackExchange.Redis.IDatabaseAsync.HashFieldGetExpireDateTimeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long[]!>!
StackExchange.Redis.IDatabaseAsync.HashFieldGetTimeToLiveAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long[]!>!
StackExchange.Redis.IDatabaseAsync.HashFieldPersistAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.PersistResult[]!>!

StackExchange.Redis.IDatabaseAsync.HashGetAllAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.HashEntry[]!>!
StackExchange.Redis.IDatabaseAsync.HashGetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.HashGetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.HashGetLeaseAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.Lease<byte>?>!
StackExchange.Redis.IDatabaseAsync.HashIncrementAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double>!
StackExchange.Redis.IDatabaseAsync.HashIncrementAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, long value = 1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.HashKeysAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.HashLengthAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.HashRandomFieldAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.HashRandomFieldsAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.HashRandomFieldsWithValuesAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.HashEntry[]!>!
StackExchange.Redis.IDatabaseAsync.HashScanAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IAsyncEnumerable<StackExchange.Redis.HashEntry>!
StackExchange.Redis.IDatabaseAsync.HashScanNoValuesAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IAsyncEnumerable<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.HashSetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.HashEntry[]! hashFields, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IDatabaseAsync.HashSetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.RedisValue value, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.HashStringLengthAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.HashValuesAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.HyperLogLogAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.HyperLogLogAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.HyperLogLogLengthAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.HyperLogLogLengthAsync(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.HyperLogLogMergeAsync(StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IDatabaseAsync.HyperLogLogMergeAsync(StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey[]! sourceKeys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IDatabaseAsync.IdentifyEndpointAsync(StackExchange.Redis.RedisKey key = default(StackExchange.Redis.RedisKey), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Net.EndPoint?>!
StackExchange.Redis.IDatabaseAsync.IsConnected(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IDatabaseAsync.KeyCopyAsync(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, int destinationDatabase = -1, bool replace = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyDeleteAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyDeleteAsync(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.KeyDumpAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<byte[]?>!
StackExchange.Redis.IDatabaseAsync.KeyEncodingAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string?>!
StackExchange.Redis.IDatabaseAsync.KeyExistsAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyExistsAsync(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.KeyExpireAsync(StackExchange.Redis.RedisKey key, System.DateTime? expiry, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyExpireAsync(StackExchange.Redis.RedisKey key, System.DateTime? expiry, StackExchange.Redis.ExpireWhen when = StackExchange.Redis.ExpireWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyExpireAsync(StackExchange.Redis.RedisKey key, System.TimeSpan? expiry, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyExpireAsync(StackExchange.Redis.RedisKey key, System.TimeSpan? expiry, StackExchange.Redis.ExpireWhen when = StackExchange.Redis.ExpireWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyExpireTimeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.DateTime?>!
StackExchange.Redis.IDatabaseAsync.KeyFrequencyAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long?>!
StackExchange.Redis.IDatabaseAsync.KeyIdleTimeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.TimeSpan?>!
StackExchange.Redis.IDatabaseAsync.KeyMigrateAsync(StackExchange.Redis.RedisKey key, System.Net.EndPoint! toServer, int toDatabase = 0, int timeoutMilliseconds = 0, StackExchange.Redis.MigrateOptions migrateOptions = StackExchange.Redis.MigrateOptions.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IDatabaseAsync.KeyMoveAsync(StackExchange.Redis.RedisKey key, int database, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyPersistAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyRandomAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisKey>!
StackExchange.Redis.IDatabaseAsync.KeyRefCountAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long?>!
StackExchange.Redis.IDatabaseAsync.KeyRenameAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisKey newKey, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyRestoreAsync(StackExchange.Redis.RedisKey key, byte[]! value, System.TimeSpan? expiry = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IDatabaseAsync.KeyTimeToLiveAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.TimeSpan?>!
StackExchange.Redis.IDatabaseAsync.KeyTouchAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.KeyTouchAsync(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.KeyTypeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisType>!
StackExchange.Redis.IDatabaseAsync.ListGetByIndexAsync(StackExchange.Redis.RedisKey key, long index, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.ListInsertAfterAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pivot, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListInsertBeforeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pivot, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListMoveAsync(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, StackExchange.Redis.ListSide sourceSide, StackExchange.Redis.ListSide destinationSide, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.ListLeftPopAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.ListLeftPopAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.ListLeftPopAsync(StackExchange.Redis.RedisKey[]! keys, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.ListPopResult>!
StackExchange.Redis.IDatabaseAsync.ListLeftPushAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListLeftPushAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListLeftPushAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListLengthAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListPositionAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue element, long rank = 1, long maxLength = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListPositionsAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue element, long count, long rank = 1, long maxLength = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long[]!>!
StackExchange.Redis.IDatabaseAsync.ListRangeAsync(StackExchange.Redis.RedisKey key, long start = 0, long stop = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.ListRemoveAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, long count = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListRightPopAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.ListRightPopAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.ListRightPopAsync(StackExchange.Redis.RedisKey[]! keys, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.ListPopResult>!
StackExchange.Redis.IDatabaseAsync.ListRightPopLeftPushAsync(StackExchange.Redis.RedisKey source, StackExchange.Redis.RedisKey destination, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.ListRightPushAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListRightPushAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListRightPushAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ListSetByIndexAsync(StackExchange.Redis.RedisKey key, long index, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IDatabaseAsync.ListTrimAsync(StackExchange.Redis.RedisKey key, long start, long stop, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IDatabaseAsync.LockExtendAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan expiry, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.LockQueryAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.LockReleaseAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.LockTakeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan expiry, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.PublishAsync(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.RedisValue message, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.ScriptEvaluateAsync(byte[]! hash, StackExchange.Redis.RedisKey[]? keys = null, StackExchange.Redis.RedisValue[]? values = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IDatabaseAsync.ScriptEvaluateAsync(StackExchange.Redis.LoadedLuaScript! script, object? parameters = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IDatabaseAsync.ScriptEvaluateAsync(StackExchange.Redis.LuaScript! script, object? parameters = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IDatabaseAsync.ScriptEvaluateAsync(string! script, StackExchange.Redis.RedisKey[]? keys = null, StackExchange.Redis.RedisValue[]? values = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IDatabaseAsync.ScriptEvaluateReadOnlyAsync(byte[]! hash, StackExchange.Redis.RedisKey[]? keys = null, StackExchange.Redis.RedisValue[]? values = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IDatabaseAsync.ScriptEvaluateReadOnlyAsync(string! script, StackExchange.Redis.RedisKey[]? keys = null, StackExchange.Redis.RedisValue[]? values = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IDatabaseAsync.SetAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SetAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SetCombineAndStoreAsync(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SetCombineAndStoreAsync(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SetCombineAsync(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SetCombineAsync(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SetContainsAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SetContainsAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool[]!>!
StackExchange.Redis.IDatabaseAsync.SetIntersectionLengthAsync(StackExchange.Redis.RedisKey[]! keys, long limit = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SetLengthAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SetMembersAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SetMoveAsync(StackExchange.Redis.RedisKey source, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SetPopAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SetPopAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.SetRandomMemberAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.SetRandomMembersAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SetRemoveAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SetRemoveAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SetScanAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IAsyncEnumerable<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.SortAndStoreAsync(StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey key, long skip = 0, long take = -1, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.SortType sortType = StackExchange.Redis.SortType.Numeric, StackExchange.Redis.RedisValue by = default(StackExchange.Redis.RedisValue), StackExchange.Redis.RedisValue[]? get = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortAsync(StackExchange.Redis.RedisKey key, long skip = 0, long take = -1, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.SortType sortType = StackExchange.Redis.SortType.Numeric, StackExchange.Redis.RedisValue by = default(StackExchange.Redis.RedisValue), StackExchange.Redis.RedisValue[]? get = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double score, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SortedSetAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double score, StackExchange.Redis.When when, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SortedSetAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double score, StackExchange.Redis.SortedSetWhen when = StackExchange.Redis.SortedSetWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SortedSetAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.SortedSetEntry[]! values, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.SortedSetEntry[]! values, StackExchange.Redis.When when, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.SortedSetEntry[]! values, StackExchange.Redis.SortedSetWhen when = StackExchange.Redis.SortedSetWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetCombineAsync(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey[]! keys, double[]? weights = null, StackExchange.Redis.Aggregate aggregate = StackExchange.Redis.Aggregate.Sum, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetCombineWithScoresAsync(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey[]! keys, double[]? weights = null, StackExchange.Redis.Aggregate aggregate = StackExchange.Redis.Aggregate.Sum, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.SortedSetEntry[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetCombineAndStoreAsync(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.Aggregate aggregate = StackExchange.Redis.Aggregate.Sum, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetCombineAndStoreAsync(StackExchange.Redis.SetOperation operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey[]! keys, double[]? weights = null, StackExchange.Redis.Aggregate aggregate = StackExchange.Redis.Aggregate.Sum, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetDecrementAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double>!
StackExchange.Redis.IDatabaseAsync.SortedSetIncrementAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double>!
StackExchange.Redis.IDatabaseAsync.SortedSetIntersectionLengthAsync(StackExchange.Redis.RedisKey[]! keys, long limit = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetLengthAsync(StackExchange.Redis.RedisKey key, double min = -Infinity, double max = Infinity, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetLengthByValueAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue min, StackExchange.Redis.RedisValue max, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetPopAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.SortedSetEntry[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetPopAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.SortedSetEntry?>!
StackExchange.Redis.IDatabaseAsync.SortedSetPopAsync(StackExchange.Redis.RedisKey[]! keys, long count, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.SortedSetPopResult>!
StackExchange.Redis.IDatabaseAsync.SortedSetRandomMemberAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.SortedSetRandomMembersAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetRandomMembersWithScoresAsync(StackExchange.Redis.RedisKey key, long count, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.SortedSetEntry[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetRangeAndStoreAsync(StackExchange.Redis.RedisKey sourceKey, StackExchange.Redis.RedisKey destinationKey, StackExchange.Redis.RedisValue start, StackExchange.Redis.RedisValue stop, StackExchange.Redis.SortedSetOrder sortedSetOrder = StackExchange.Redis.SortedSetOrder.ByRank, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, long skip = 0, long? take = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetRangeByRankAsync(StackExchange.Redis.RedisKey key, long start = 0, long stop = -1, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetRangeByRankWithScoresAsync(StackExchange.Redis.RedisKey key, long start = 0, long stop = -1, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.SortedSetEntry[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetRangeByScoreAsync(StackExchange.Redis.RedisKey key, double start = -Infinity, double stop = Infinity, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, long skip = 0, long take = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetRangeByScoreWithScoresAsync(StackExchange.Redis.RedisKey key, double start = -Infinity, double stop = Infinity, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, long skip = 0, long take = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.SortedSetEntry[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetRangeByValueAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue min = default(StackExchange.Redis.RedisValue), StackExchange.Redis.RedisValue max = default(StackExchange.Redis.RedisValue), StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, long skip = 0, long take = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetRangeByValueAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue min, StackExchange.Redis.RedisValue max, StackExchange.Redis.Exclude exclude, long skip, long take = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetRankAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.Order order = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long?>!
StackExchange.Redis.IDatabaseAsync.SortedSetRemoveAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SortedSetRemoveAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! members, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetRemoveRangeByRankAsync(StackExchange.Redis.RedisKey key, long start, long stop, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetRemoveRangeByScoreAsync(StackExchange.Redis.RedisKey key, double start, double stop, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetRemoveRangeByValueAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue min, StackExchange.Redis.RedisValue max, StackExchange.Redis.Exclude exclude = StackExchange.Redis.Exclude.None, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.SortedSetScanAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IAsyncEnumerable<StackExchange.Redis.SortedSetEntry>!
StackExchange.Redis.IDatabaseAsync.SortedSetScoreAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double?>!
StackExchange.Redis.IDatabaseAsync.SortedSetScoresAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! members, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double?[]!>!
StackExchange.Redis.IDatabaseAsync.SortedSetUpdateAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, double score, StackExchange.Redis.SortedSetWhen when = StackExchange.Redis.SortedSetWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.SortedSetUpdateAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.SortedSetEntry[]! values, StackExchange.Redis.SortedSetWhen when = StackExchange.Redis.SortedSetWhen.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StreamAcknowledgeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue messageId, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StreamAcknowledgeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue[]! messageIds, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StreamAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.NameValueEntry[]! streamPairs, StackExchange.Redis.RedisValue? messageId = null, int? maxLength = null, bool useApproximateMaxLength = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StreamAddAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue streamField, StackExchange.Redis.RedisValue streamValue, StackExchange.Redis.RedisValue? messageId = null, int? maxLength = null, bool useApproximateMaxLength = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StreamAutoClaimAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue consumerGroup, StackExchange.Redis.RedisValue claimingConsumer, long minIdleTimeInMs, StackExchange.Redis.RedisValue startAtId, int? count = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamAutoClaimResult>!
StackExchange.Redis.IDatabaseAsync.StreamAutoClaimIdsOnlyAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue consumerGroup, StackExchange.Redis.RedisValue claimingConsumer, long minIdleTimeInMs, StackExchange.Redis.RedisValue startAtId, int? count = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamAutoClaimIdsOnlyResult>!
StackExchange.Redis.IDatabaseAsync.StreamClaimAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue consumerGroup, StackExchange.Redis.RedisValue claimingConsumer, long minIdleTimeInMs, StackExchange.Redis.RedisValue[]! messageIds, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamEntry[]!>!
StackExchange.Redis.IDatabaseAsync.StreamClaimIdsOnlyAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue consumerGroup, StackExchange.Redis.RedisValue claimingConsumer, long minIdleTimeInMs, StackExchange.Redis.RedisValue[]! messageIds, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.StreamConsumerGroupSetPositionAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue position, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StreamConsumerInfoAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamConsumerInfo[]!>!
StackExchange.Redis.IDatabaseAsync.StreamCreateConsumerGroupAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue? position = null, bool createStream = true, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StreamCreateConsumerGroupAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue? position, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StreamDeleteAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue[]! messageIds, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StreamDeleteConsumerAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StreamDeleteConsumerGroupAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StreamGroupInfoAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamGroupInfo[]!>!
StackExchange.Redis.IDatabaseAsync.StreamInfoAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamInfo>!
StackExchange.Redis.IDatabaseAsync.StreamLengthAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StreamPendingAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamPendingInfo>!
StackExchange.Redis.IDatabaseAsync.StreamPendingMessagesAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, int count, StackExchange.Redis.RedisValue consumerName, StackExchange.Redis.RedisValue? minId = null, StackExchange.Redis.RedisValue? maxId = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamPendingMessageInfo[]!>!
StackExchange.Redis.IDatabaseAsync.StreamRangeAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue? minId = null, StackExchange.Redis.RedisValue? maxId = null, int? count = null, StackExchange.Redis.Order messageOrder = StackExchange.Redis.Order.Ascending, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamEntry[]!>!
StackExchange.Redis.IDatabaseAsync.StreamReadAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue position, int? count = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamEntry[]!>!
StackExchange.Redis.IDatabaseAsync.StreamReadAsync(StackExchange.Redis.StreamPosition[]! streamPositions, int? countPerStream = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisStream[]!>!
StackExchange.Redis.IDatabaseAsync.StreamReadGroupAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, StackExchange.Redis.RedisValue? position = null, int? count = null, bool noAck = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamEntry[]!>!
StackExchange.Redis.IDatabaseAsync.StreamReadGroupAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, StackExchange.Redis.RedisValue? position, int? count, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<StackExchange.Redis.StreamEntry[]!>!
StackExchange.Redis.IDatabaseAsync.StreamReadGroupAsync(StackExchange.Redis.StreamPosition[]! streamPositions, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, int? countPerStream = null, bool noAck = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisStream[]!>!
StackExchange.Redis.IDatabaseAsync.StreamReadGroupAsync(StackExchange.Redis.StreamPosition[]! streamPositions, StackExchange.Redis.RedisValue groupName, StackExchange.Redis.RedisValue consumerName, int? countPerStream, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisStream[]!>!
StackExchange.Redis.IDatabaseAsync.StreamTrimAsync(StackExchange.Redis.RedisKey key, int maxLength, bool useApproximateMaxLength = false, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringAppendAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringBitCountAsync(StackExchange.Redis.RedisKey key, long start, long end, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringBitCountAsync(StackExchange.Redis.RedisKey key, long start = 0, long end = -1, StackExchange.Redis.StringIndexType indexType = StackExchange.Redis.StringIndexType.Byte, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringBitOperationAsync(StackExchange.Redis.Bitwise operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second = default(StackExchange.Redis.RedisKey), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringBitOperationAsync(StackExchange.Redis.Bitwise operation, StackExchange.Redis.RedisKey destination, StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringBitPositionAsync(StackExchange.Redis.RedisKey key, bool bit, long start, long end, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringBitPositionAsync(StackExchange.Redis.RedisKey key, bool bit, long start = 0, long end = -1, StackExchange.Redis.StringIndexType indexType = StackExchange.Redis.StringIndexType.Byte, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringDecrementAsync(StackExchange.Redis.RedisKey key, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double>!
StackExchange.Redis.IDatabaseAsync.StringDecrementAsync(StackExchange.Redis.RedisKey key, long value = 1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringGetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StringGetAsync(StackExchange.Redis.RedisKey[]! keys, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue[]!>!
StackExchange.Redis.IDatabaseAsync.StringGetBitAsync(StackExchange.Redis.RedisKey key, long offset, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StringGetDeleteAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StringGetLeaseAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.Lease<byte>?>!
StackExchange.Redis.IDatabaseAsync.StringGetRangeAsync(StackExchange.Redis.RedisKey key, long start, long end, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StringGetSetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StringGetSetExpiryAsync(StackExchange.Redis.RedisKey key, System.TimeSpan? expiry, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StringGetSetExpiryAsync(StackExchange.Redis.RedisKey key, System.DateTime expiry, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StringGetWithExpiryAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValueWithExpiry>!
StackExchange.Redis.IDatabaseAsync.StringIncrementAsync(StackExchange.Redis.RedisKey key, double value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<double>!
StackExchange.Redis.IDatabaseAsync.StringIncrementAsync(StackExchange.Redis.RedisKey key, long value = 1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringLengthAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringLongestCommonSubsequenceAsync(StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string?>!
StackExchange.Redis.IDatabaseAsync.StringLongestCommonSubsequenceLengthAsync(StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IDatabaseAsync.StringLongestCommonSubsequenceWithMatchesAsync(StackExchange.Redis.RedisKey first, StackExchange.Redis.RedisKey second, long minLength = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.LCSMatchResult>!
StackExchange.Redis.IDatabaseAsync.StringSetAndGetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry = null, bool keepTtl = false, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StringSetAndGetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry, StackExchange.Redis.When when, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IDatabaseAsync.StringSetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry = null, bool keepTtl = false, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StringSetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry, StackExchange.Redis.When when) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StringSetAsync(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value, System.TimeSpan? expiry, StackExchange.Redis.When when, StackExchange.Redis.CommandFlags flags) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StringSetAsync(System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisKey, StackExchange.Redis.RedisValue>[]! values, StackExchange.Redis.When when = StackExchange.Redis.When.Always, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StringSetBitAsync(StackExchange.Redis.RedisKey key, long offset, bool bit, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IDatabaseAsync.StringSetRangeAsync(StackExchange.Redis.RedisKey key, long offset, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.InternalErrorEventArgs
StackExchange.Redis.InternalErrorEventArgs.ConnectionType.get -> StackExchange.Redis.ConnectionType
StackExchange.Redis.InternalErrorEventArgs.EndPoint.get -> System.Net.EndPoint?
StackExchange.Redis.InternalErrorEventArgs.Exception.get -> System.Exception!
StackExchange.Redis.InternalErrorEventArgs.InternalErrorEventArgs(object! sender, System.Net.EndPoint! endpoint, StackExchange.Redis.ConnectionType connectionType, System.Exception! exception, string! origin) -> void
StackExchange.Redis.InternalErrorEventArgs.Origin.get -> string?
StackExchange.Redis.IReconnectRetryPolicy
StackExchange.Redis.IReconnectRetryPolicy.ShouldRetry(long currentRetryCount, int timeElapsedMillisecondsSinceLastRetry) -> bool
StackExchange.Redis.IRedis
StackExchange.Redis.IRedis.Ping(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.TimeSpan
StackExchange.Redis.IRedisAsync
StackExchange.Redis.IRedisAsync.Multiplexer.get -> StackExchange.Redis.IConnectionMultiplexer!
StackExchange.Redis.IRedisAsync.PingAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.TimeSpan>!
StackExchange.Redis.IRedisAsync.TryWait(System.Threading.Tasks.Task! task) -> bool
StackExchange.Redis.IRedisAsync.Wait(System.Threading.Tasks.Task! task) -> void
StackExchange.Redis.IRedisAsync.Wait<T>(System.Threading.Tasks.Task<T>! task) -> T
StackExchange.Redis.IRedisAsync.WaitAll(params System.Threading.Tasks.Task![]! tasks) -> void
StackExchange.Redis.IScanningCursor
StackExchange.Redis.IScanningCursor.Cursor.get -> long
StackExchange.Redis.IScanningCursor.PageOffset.get -> int
StackExchange.Redis.IScanningCursor.PageSize.get -> int
StackExchange.Redis.IServer
StackExchange.Redis.IServer.AllowReplicaWrites.get -> bool
StackExchange.Redis.IServer.AllowReplicaWrites.set -> void
StackExchange.Redis.IServer.AllowSlaveWrites.get -> bool
StackExchange.Redis.IServer.AllowSlaveWrites.set -> void
StackExchange.Redis.IServer.ClientKill(long? id = null, StackExchange.Redis.ClientType? clientType = null, System.Net.EndPoint? endpoint = null, bool skipMe = true, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IServer.ClientKill(System.Net.EndPoint! endpoint, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.ClientKill(StackExchange.Redis.ClientKillFilter! filter, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IServer.ClientKillAsync(long? id = null, StackExchange.Redis.ClientType? clientType = null, System.Net.EndPoint? endpoint = null, bool skipMe = true, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IServer.ClientKillAsync(System.Net.EndPoint! endpoint, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.ClientKillAsync(StackExchange.Redis.ClientKillFilter! filter, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IServer.ClientList(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.ClientInfo![]!
StackExchange.Redis.IServer.ClientListAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.ClientInfo![]!>!
StackExchange.Redis.IServer.ClusterConfiguration.get -> StackExchange.Redis.ClusterConfiguration?
StackExchange.Redis.IServer.ClusterNodes(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.ClusterConfiguration?
StackExchange.Redis.IServer.ClusterNodesAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.ClusterConfiguration?>!
StackExchange.Redis.IServer.ClusterNodesRaw(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string?
StackExchange.Redis.IServer.ClusterNodesRawAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string?>!
StackExchange.Redis.IServer.ConfigGet(StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.KeyValuePair<string!, string!>[]!
StackExchange.Redis.IServer.ConfigGetAsync(StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Collections.Generic.KeyValuePair<string!, string!>[]!>!
StackExchange.Redis.IServer.ConfigResetStatistics(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.ConfigResetStatisticsAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.ConfigRewrite(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.ConfigRewriteAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.ConfigSet(StackExchange.Redis.RedisValue setting, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.ConfigSetAsync(StackExchange.Redis.RedisValue setting, StackExchange.Redis.RedisValue value, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.CommandCount(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IServer.CommandCountAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IServer.CommandGetKeys(StackExchange.Redis.RedisValue[]! command, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisKey[]!
StackExchange.Redis.IServer.CommandGetKeysAsync(StackExchange.Redis.RedisValue[]! command, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisKey[]!>!
StackExchange.Redis.IServer.CommandList(StackExchange.Redis.RedisValue? moduleName = null, StackExchange.Redis.RedisValue? category = null, StackExchange.Redis.RedisValue? pattern = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string![]!
StackExchange.Redis.IServer.CommandListAsync(StackExchange.Redis.RedisValue? moduleName = null, StackExchange.Redis.RedisValue? category = null, StackExchange.Redis.RedisValue? pattern = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string![]!>!
StackExchange.Redis.IServer.DatabaseCount.get -> int
StackExchange.Redis.IServer.DatabaseSize(int database = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IServer.DatabaseSizeAsync(int database = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IServer.Echo(StackExchange.Redis.RedisValue message, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisValue
StackExchange.Redis.IServer.EchoAsync(StackExchange.Redis.RedisValue message, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisValue>!
StackExchange.Redis.IServer.EndPoint.get -> System.Net.EndPoint!
StackExchange.Redis.IServer.Execute(string! command, params object![]! args) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IServer.Execute(string! command, System.Collections.Generic.ICollection<object!>! args, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IServer.ExecuteAsync(string! command, params object![]! args) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IServer.ExecuteAsync(string! command, System.Collections.Generic.ICollection<object!>! args, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IServer.Features.get -> StackExchange.Redis.RedisFeatures
StackExchange.Redis.IServer.FlushAllDatabases(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.FlushAllDatabasesAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.FlushDatabase(int database = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.FlushDatabaseAsync(int database = -1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.GetCounters() -> StackExchange.Redis.ServerCounters!
StackExchange.Redis.IServer.Info(StackExchange.Redis.RedisValue section = default(StackExchange.Redis.RedisValue), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Linq.IGrouping<string!, System.Collections.Generic.KeyValuePair<string!, string!>>![]!
StackExchange.Redis.IServer.InfoAsync(StackExchange.Redis.RedisValue section = default(StackExchange.Redis.RedisValue), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Linq.IGrouping<string!, System.Collections.Generic.KeyValuePair<string!, string!>>![]!>!
StackExchange.Redis.IServer.InfoRaw(StackExchange.Redis.RedisValue section = default(StackExchange.Redis.RedisValue), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string?
StackExchange.Redis.IServer.InfoRawAsync(StackExchange.Redis.RedisValue section = default(StackExchange.Redis.RedisValue), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string?>!
StackExchange.Redis.IServer.IsConnected.get -> bool
StackExchange.Redis.IServer.IsReplica.get -> bool
StackExchange.Redis.IServer.IsSlave.get -> bool
StackExchange.Redis.IServer.Keys(int database = -1, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.RedisKey>!
StackExchange.Redis.IServer.Keys(int database, StackExchange.Redis.RedisValue pattern, int pageSize, StackExchange.Redis.CommandFlags flags) -> System.Collections.Generic.IEnumerable<StackExchange.Redis.RedisKey>!
StackExchange.Redis.IServer.KeysAsync(int database = -1, StackExchange.Redis.RedisValue pattern = default(StackExchange.Redis.RedisValue), int pageSize = 250, long cursor = 0, int pageOffset = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.IAsyncEnumerable<StackExchange.Redis.RedisKey>!
StackExchange.Redis.IServer.LastSave(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.DateTime
StackExchange.Redis.IServer.LastSaveAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.DateTime>!
StackExchange.Redis.IServer.LatencyDoctor(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string!
StackExchange.Redis.IServer.LatencyDoctorAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string!>!
StackExchange.Redis.IServer.LatencyHistory(string! eventName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.LatencyHistoryEntry[]!
StackExchange.Redis.IServer.LatencyHistoryAsync(string! eventName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.LatencyHistoryEntry[]!>!
StackExchange.Redis.IServer.LatencyLatest(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.LatencyLatestEntry[]!
StackExchange.Redis.IServer.LatencyLatestAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.LatencyLatestEntry[]!>!
StackExchange.Redis.IServer.LatencyReset(string![]? eventNames = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IServer.LatencyResetAsync(string![]? eventNames = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IServer.MakeMaster(StackExchange.Redis.ReplicationChangeOptions options, System.IO.TextWriter? log = null) -> void
StackExchange.Redis.IServer.MakePrimaryAsync(StackExchange.Redis.ReplicationChangeOptions options, System.IO.TextWriter? log = null) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.MemoryAllocatorStats(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string?
StackExchange.Redis.IServer.MemoryAllocatorStatsAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string?>!
StackExchange.Redis.IServer.MemoryDoctor(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> string!
StackExchange.Redis.IServer.MemoryDoctorAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<string!>!
StackExchange.Redis.IServer.MemoryPurge(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.MemoryPurgeAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.MemoryStats(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.IServer.MemoryStatsAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.IServer.ReplicaOf(System.Net.EndPoint! master, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.ReplicaOfAsync(System.Net.EndPoint! master, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.Role(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.Role!
StackExchange.Redis.IServer.RoleAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.Role!>!
StackExchange.Redis.IServer.Save(StackExchange.Redis.SaveType type, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.SaveAsync(StackExchange.Redis.SaveType type, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.ScriptExists(byte[]! sha1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IServer.ScriptExists(string! script, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.IServer.ScriptExistsAsync(byte[]! sha1, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IServer.ScriptExistsAsync(string! script, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.IServer.ScriptFlush(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.ScriptFlushAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.ScriptLoad(StackExchange.Redis.LuaScript! script, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.LoadedLuaScript!
StackExchange.Redis.IServer.ScriptLoad(string! script, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> byte[]!
StackExchange.Redis.IServer.ScriptLoadAsync(StackExchange.Redis.LuaScript! script, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.LoadedLuaScript!>!
StackExchange.Redis.IServer.ScriptLoadAsync(string! script, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<byte[]!>!
StackExchange.Redis.IServer.SentinelFailover(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.SentinelFailoverAsync(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.SentinelGetMasterAddressByName(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Net.EndPoint?
StackExchange.Redis.IServer.SentinelGetMasterAddressByNameAsync(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Net.EndPoint?>!
StackExchange.Redis.IServer.SentinelGetReplicaAddresses(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Net.EndPoint![]!
StackExchange.Redis.IServer.SentinelGetReplicaAddressesAsync(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Net.EndPoint![]!>!
StackExchange.Redis.IServer.SentinelGetSentinelAddresses(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Net.EndPoint![]!
StackExchange.Redis.IServer.SentinelGetSentinelAddressesAsync(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Net.EndPoint![]!>!
StackExchange.Redis.IServer.SentinelMaster(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.KeyValuePair<string!, string!>[]!
StackExchange.Redis.IServer.SentinelMasterAsync(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Collections.Generic.KeyValuePair<string!, string!>[]!>!
StackExchange.Redis.IServer.SentinelMasters(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.KeyValuePair<string!, string!>[]![]!
StackExchange.Redis.IServer.SentinelMastersAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Collections.Generic.KeyValuePair<string!, string!>[]![]!>!
StackExchange.Redis.IServer.SentinelReplicas(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.KeyValuePair<string!, string!>[]![]!
StackExchange.Redis.IServer.SentinelReplicasAsync(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Collections.Generic.KeyValuePair<string!, string!>[]![]!>!
StackExchange.Redis.IServer.SentinelSentinels(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.KeyValuePair<string!, string!>[]![]!
StackExchange.Redis.IServer.SentinelSentinelsAsync(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Collections.Generic.KeyValuePair<string!, string!>[]![]!>!
StackExchange.Redis.IServer.SentinelSlaves(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Collections.Generic.KeyValuePair<string!, string!>[]![]!
StackExchange.Redis.IServer.SentinelSlavesAsync(string! serviceName, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Collections.Generic.KeyValuePair<string!, string!>[]![]!>!
StackExchange.Redis.IServer.ServerType.get -> StackExchange.Redis.ServerType
StackExchange.Redis.IServer.Shutdown(StackExchange.Redis.ShutdownMode shutdownMode = StackExchange.Redis.ShutdownMode.Default, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.SlaveOf(System.Net.EndPoint! master, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.SlaveOfAsync(System.Net.EndPoint! master, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.SlowlogGet(int count = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.CommandTrace![]!
StackExchange.Redis.IServer.SlowlogGetAsync(int count = 0, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.CommandTrace![]!>!
StackExchange.Redis.IServer.SlowlogReset(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.SlowlogResetAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.SubscriptionChannels(StackExchange.Redis.RedisChannel pattern = default(StackExchange.Redis.RedisChannel), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisChannel[]!
StackExchange.Redis.IServer.SubscriptionChannelsAsync(StackExchange.Redis.RedisChannel pattern = default(StackExchange.Redis.RedisChannel), StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisChannel[]!>!
StackExchange.Redis.IServer.SubscriptionPatternCount(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IServer.SubscriptionPatternCountAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IServer.SubscriptionSubscriberCount(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.IServer.SubscriptionSubscriberCountAsync(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.IServer.SwapDatabases(int first, int second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.IServer.SwapDatabasesAsync(int first, int second, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.IServer.Time(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.DateTime
StackExchange.Redis.IServer.TimeAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.DateTime>!
StackExchange.Redis.IServer.Version.get -> System.Version!
StackExchange.Redis.ISubscriber
StackExchange.Redis.ISubscriber.IdentifyEndpoint(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Net.EndPoint?
StackExchange.Redis.ISubscriber.IdentifyEndpointAsync(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<System.Net.EndPoint?>!
StackExchange.Redis.ISubscriber.IsConnected(StackExchange.Redis.RedisChannel channel = default(StackExchange.Redis.RedisChannel)) -> bool
StackExchange.Redis.ISubscriber.Publish(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.RedisValue message, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> long
StackExchange.Redis.ISubscriber.PublishAsync(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.RedisValue message, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<long>!
StackExchange.Redis.ISubscriber.Subscribe(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.ChannelMessageQueue!
StackExchange.Redis.ISubscriber.Subscribe(StackExchange.Redis.RedisChannel channel, System.Action<StackExchange.Redis.RedisChannel, StackExchange.Redis.RedisValue>! handler, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.ISubscriber.SubscribeAsync(StackExchange.Redis.RedisChannel channel, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.ChannelMessageQueue!>!
StackExchange.Redis.ISubscriber.SubscribeAsync(StackExchange.Redis.RedisChannel channel, System.Action<StackExchange.Redis.RedisChannel, StackExchange.Redis.RedisValue>! handler, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.ISubscriber.SubscribedEndpoint(StackExchange.Redis.RedisChannel channel) -> System.Net.EndPoint?
StackExchange.Redis.ISubscriber.Unsubscribe(StackExchange.Redis.RedisChannel channel, System.Action<StackExchange.Redis.RedisChannel, StackExchange.Redis.RedisValue>? handler = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.ISubscriber.UnsubscribeAll(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> void
StackExchange.Redis.ISubscriber.UnsubscribeAllAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.ISubscriber.UnsubscribeAsync(StackExchange.Redis.RedisChannel channel, System.Action<StackExchange.Redis.RedisChannel, StackExchange.Redis.RedisValue>? handler = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task!
StackExchange.Redis.ITransaction
StackExchange.Redis.ITransaction.AddCondition(StackExchange.Redis.Condition! condition) -> StackExchange.Redis.ConditionResult!
StackExchange.Redis.ITransaction.Execute(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> bool
StackExchange.Redis.ITransaction.ExecuteAsync(StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<bool>!
StackExchange.Redis.KeyspaceIsolation.DatabaseExtensions
StackExchange.Redis.LatencyHistoryEntry
StackExchange.Redis.LatencyHistoryEntry.DurationMilliseconds.get -> int
StackExchange.Redis.LatencyHistoryEntry.LatencyHistoryEntry() -> void
StackExchange.Redis.LatencyHistoryEntry.Timestamp.get -> System.DateTime
StackExchange.Redis.LatencyLatestEntry
StackExchange.Redis.LatencyLatestEntry.DurationMilliseconds.get -> int
StackExchange.Redis.LatencyLatestEntry.EventName.get -> string!
StackExchange.Redis.LatencyLatestEntry.LatencyLatestEntry() -> void
StackExchange.Redis.LatencyLatestEntry.MaxDurationMilliseconds.get -> int
StackExchange.Redis.LatencyLatestEntry.Timestamp.get -> System.DateTime
StackExchange.Redis.Lease<T>
StackExchange.Redis.Lease<T>.ArraySegment.get -> System.ArraySegment<T>
StackExchange.Redis.Lease<T>.Dispose() -> void
StackExchange.Redis.Lease<T>.Length.get -> int
StackExchange.Redis.Lease<T>.Memory.get -> System.Memory<T>
StackExchange.Redis.Lease<T>.Span.get -> System.Span<T>
StackExchange.Redis.LinearRetry
StackExchange.Redis.LinearRetry.LinearRetry(int maxRetryElapsedTimeAllowedMilliseconds) -> void
StackExchange.Redis.LinearRetry.ShouldRetry(long currentRetryCount, int timeElapsedMillisecondsSinceLastRetry) -> bool
StackExchange.Redis.LoadedLuaScript
StackExchange.Redis.LoadedLuaScript.Evaluate(StackExchange.Redis.IDatabase! db, object? ps = null, StackExchange.Redis.RedisKey? withKeyPrefix = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.LoadedLuaScript.EvaluateAsync(StackExchange.Redis.IDatabaseAsync! db, object? ps = null, StackExchange.Redis.RedisKey? withKeyPrefix = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.LoadedLuaScript.ExecutableScript.get -> string!
StackExchange.Redis.LoadedLuaScript.Hash.get -> byte[]!
StackExchange.Redis.LoadedLuaScript.OriginalScript.get -> string!
StackExchange.Redis.LCSMatchResult
StackExchange.Redis.LCSMatchResult.IsEmpty.get -> bool
StackExchange.Redis.LCSMatchResult.LCSMatchResult() -> void
StackExchange.Redis.LCSMatchResult.LongestMatchLength.get -> long
StackExchange.Redis.LCSMatchResult.Matches.get -> StackExchange.Redis.LCSMatchResult.LCSMatch[]!
StackExchange.Redis.LCSMatchResult.LCSMatch
StackExchange.Redis.LCSMatchResult.LCSMatch.LCSMatch() -> void
StackExchange.Redis.LCSMatchResult.LCSMatch.FirstStringIndex.get -> long
StackExchange.Redis.LCSMatchResult.LCSMatch.SecondStringIndex.get -> long
StackExchange.Redis.LCSMatchResult.LCSMatch.Length.get -> long
StackExchange.Redis.ListPopResult
StackExchange.Redis.ListPopResult.IsNull.get -> bool
StackExchange.Redis.ListPopResult.Key.get -> StackExchange.Redis.RedisKey
StackExchange.Redis.ListPopResult.ListPopResult() -> void
StackExchange.Redis.ListPopResult.Values.get -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.ListSide
StackExchange.Redis.ListSide.Left = 0 -> StackExchange.Redis.ListSide
StackExchange.Redis.ListSide.Right = 1 -> StackExchange.Redis.ListSide
StackExchange.Redis.LuaScript
StackExchange.Redis.LuaScript.Evaluate(StackExchange.Redis.IDatabase! db, object? ps = null, StackExchange.Redis.RedisKey? withKeyPrefix = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.RedisResult!
StackExchange.Redis.LuaScript.EvaluateAsync(StackExchange.Redis.IDatabaseAsync! db, object? ps = null, StackExchange.Redis.RedisKey? withKeyPrefix = null, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.RedisResult!>!
StackExchange.Redis.LuaScript.ExecutableScript.get -> string!
StackExchange.Redis.LuaScript.Load(StackExchange.Redis.IServer! server, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> StackExchange.Redis.LoadedLuaScript!
StackExchange.Redis.LuaScript.LoadAsync(StackExchange.Redis.IServer! server, StackExchange.Redis.CommandFlags flags = StackExchange.Redis.CommandFlags.None) -> System.Threading.Tasks.Task<StackExchange.Redis.LoadedLuaScript!>!
StackExchange.Redis.LuaScript.OriginalScript.get -> string!
StackExchange.Redis.Maintenance.AzureMaintenanceEvent
StackExchange.Redis.Maintenance.AzureMaintenanceEvent.IPAddress.get -> System.Net.IPAddress?
StackExchange.Redis.Maintenance.AzureMaintenanceEvent.IsReplica.get -> bool
StackExchange.Redis.Maintenance.AzureMaintenanceEvent.NonSslPort.get -> int
StackExchange.Redis.Maintenance.AzureMaintenanceEvent.NotificationType.get -> StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.AzureMaintenanceEvent.NotificationTypeString.get -> string!
StackExchange.Redis.Maintenance.AzureMaintenanceEvent.SslPort.get -> int
StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.AzureNotificationType.NodeMaintenanceEnded = 4 -> StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.AzureNotificationType.NodeMaintenanceFailoverComplete = 5 -> StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.AzureNotificationType.NodeMaintenanceScaleComplete = 6 -> StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.AzureNotificationType.NodeMaintenanceScheduled = 1 -> StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.AzureNotificationType.NodeMaintenanceStart = 3 -> StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.AzureNotificationType.NodeMaintenanceStarting = 2 -> StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.AzureNotificationType.Unknown = 0 -> StackExchange.Redis.Maintenance.AzureNotificationType
StackExchange.Redis.Maintenance.ServerMaintenanceEvent
StackExchange.Redis.Maintenance.ServerMaintenanceEvent.RawMessage.get -> string?
StackExchange.Redis.Maintenance.ServerMaintenanceEvent.ReceivedTimeUtc.get -> System.DateTime
StackExchange.Redis.Maintenance.ServerMaintenanceEvent.StartTimeUtc.get -> System.DateTime?
StackExchange.Redis.MigrateOptions
StackExchange.Redis.MigrateOptions.Copy = 1 -> StackExchange.Redis.MigrateOptions
StackExchange.Redis.MigrateOptions.None = 0 -> StackExchange.Redis.MigrateOptions
StackExchange.Redis.MigrateOptions.Replace = 2 -> StackExchange.Redis.MigrateOptions
StackExchange.Redis.NameValueEntry
StackExchange.Redis.NameValueEntry.Equals(StackExchange.Redis.NameValueEntry other) -> bool
StackExchange.Redis.NameValueEntry.Name.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.NameValueEntry.NameValueEntry() -> void
StackExchange.Redis.NameValueEntry.NameValueEntry(StackExchange.Redis.RedisValue name, StackExchange.Redis.RedisValue value) -> void
StackExchange.Redis.NameValueEntry.Value.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.Order
StackExchange.Redis.Order.Ascending = 0 -> StackExchange.Redis.Order
StackExchange.Redis.Order.Descending = 1 -> StackExchange.Redis.Order
StackExchange.Redis.PersistResult
StackExchange.Redis.PersistResult.ConditionNotMet = -1 -> StackExchange.Redis.PersistResult
StackExchange.Redis.PersistResult.NoSuchField = -2 -> StackExchange.Redis.PersistResult
StackExchange.Redis.PersistResult.Success = 1 -> StackExchange.Redis.PersistResult
StackExchange.Redis.Profiling.IProfiledCommand
StackExchange.Redis.Profiling.IProfiledCommand.Command.get -> string!
StackExchange.Redis.Profiling.IProfiledCommand.CommandCreated.get -> System.DateTime
StackExchange.Redis.Profiling.IProfiledCommand.CreationToEnqueued.get -> System.TimeSpan
StackExchange.Redis.Profiling.IProfiledCommand.Db.get -> int
StackExchange.Redis.Profiling.IProfiledCommand.ElapsedTime.get -> System.TimeSpan
StackExchange.Redis.Profiling.IProfiledCommand.EndPoint.get -> System.Net.EndPoint!
StackExchange.Redis.Profiling.IProfiledCommand.EnqueuedToSending.get -> System.TimeSpan
StackExchange.Redis.Profiling.IProfiledCommand.Flags.get -> StackExchange.Redis.CommandFlags
StackExchange.Redis.Profiling.IProfiledCommand.ResponseToCompletion.get -> System.TimeSpan
StackExchange.Redis.Profiling.IProfiledCommand.RetransmissionOf.get -> StackExchange.Redis.Profiling.IProfiledCommand?
StackExchange.Redis.Profiling.IProfiledCommand.RetransmissionReason.get -> StackExchange.Redis.RetransmissionReasonType?
StackExchange.Redis.Profiling.IProfiledCommand.SentToResponse.get -> System.TimeSpan
StackExchange.Redis.Profiling.ProfiledCommandEnumerable
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Count() -> int
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Count(System.Func<StackExchange.Redis.Profiling.IProfiledCommand!, bool>! predicate) -> int
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Enumerator
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Enumerator.Current.get -> StackExchange.Redis.Profiling.IProfiledCommand!
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Enumerator.Dispose() -> void
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Enumerator.Enumerator() -> void
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Enumerator.MoveNext() -> bool
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Enumerator.Reset() -> void
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.GetEnumerator() -> StackExchange.Redis.Profiling.ProfiledCommandEnumerable.Enumerator
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.ProfiledCommandEnumerable() -> void
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.ToArray() -> StackExchange.Redis.Profiling.IProfiledCommand![]!
StackExchange.Redis.Profiling.ProfiledCommandEnumerable.ToList() -> System.Collections.Generic.List<StackExchange.Redis.Profiling.IProfiledCommand!>!
StackExchange.Redis.Profiling.ProfilingSession
StackExchange.Redis.Profiling.ProfilingSession.FinishProfiling() -> StackExchange.Redis.Profiling.ProfiledCommandEnumerable
StackExchange.Redis.Profiling.ProfilingSession.ProfilingSession(object? userToken = null) -> void
StackExchange.Redis.Profiling.ProfilingSession.UserToken.get -> object?
StackExchange.Redis.Proxy
StackExchange.Redis.Proxy.Envoyproxy = 2 -> StackExchange.Redis.Proxy
StackExchange.Redis.Proxy.None = 0 -> StackExchange.Redis.Proxy
StackExchange.Redis.Proxy.Twemproxy = 1 -> StackExchange.Redis.Proxy
StackExchange.Redis.RedisChannel
StackExchange.Redis.RedisChannel.Equals(StackExchange.Redis.RedisChannel other) -> bool
StackExchange.Redis.RedisChannel.IsNullOrEmpty.get -> bool
StackExchange.Redis.RedisChannel.IsPattern.get -> bool
StackExchange.Redis.RedisChannel.IsSharded.get -> bool
StackExchange.Redis.RedisChannel.PatternMode
StackExchange.Redis.RedisChannel.PatternMode.Auto = 0 -> StackExchange.Redis.RedisChannel.PatternMode
StackExchange.Redis.RedisChannel.PatternMode.Literal = 1 -> StackExchange.Redis.RedisChannel.PatternMode
StackExchange.Redis.RedisChannel.PatternMode.Pattern = 2 -> StackExchange.Redis.RedisChannel.PatternMode
StackExchange.Redis.RedisChannel.RedisChannel() -> void
StackExchange.Redis.RedisChannel.RedisChannel(byte[]? value, StackExchange.Redis.RedisChannel.PatternMode mode) -> void
StackExchange.Redis.RedisChannel.RedisChannel(string! value, StackExchange.Redis.RedisChannel.PatternMode mode) -> void
StackExchange.Redis.RedisCommandException
StackExchange.Redis.RedisCommandException.RedisCommandException(string! message) -> void
StackExchange.Redis.RedisCommandException.RedisCommandException(string! message, System.Exception! innerException) -> void
StackExchange.Redis.RedisConnectionException
StackExchange.Redis.RedisConnectionException.CommandStatus.get -> StackExchange.Redis.CommandStatus
StackExchange.Redis.RedisConnectionException.FailureType.get -> StackExchange.Redis.ConnectionFailureType
StackExchange.Redis.RedisConnectionException.RedisConnectionException(StackExchange.Redis.ConnectionFailureType failureType, string! message) -> void
StackExchange.Redis.RedisConnectionException.RedisConnectionException(StackExchange.Redis.ConnectionFailureType failureType, string! message, System.Exception? innerException) -> void
StackExchange.Redis.RedisConnectionException.RedisConnectionException(StackExchange.Redis.ConnectionFailureType failureType, string! message, System.Exception? innerException, StackExchange.Redis.CommandStatus commandStatus) -> void
StackExchange.Redis.RedisErrorEventArgs
StackExchange.Redis.RedisErrorEventArgs.EndPoint.get -> System.Net.EndPoint!
StackExchange.Redis.RedisErrorEventArgs.Message.get -> string!
StackExchange.Redis.RedisErrorEventArgs.RedisErrorEventArgs(object! sender, System.Net.EndPoint! endpoint, string! message) -> void
StackExchange.Redis.RedisException
StackExchange.Redis.RedisException.RedisException(string! message) -> void
StackExchange.Redis.RedisException.RedisException(string! message, System.Exception? innerException) -> void
StackExchange.Redis.RedisException.RedisException(System.Runtime.Serialization.SerializationInfo! info, System.Runtime.Serialization.StreamingContext ctx) -> void
StackExchange.Redis.RedisFeatures
StackExchange.Redis.RedisFeatures.BitwiseOperations.get -> bool
StackExchange.Redis.RedisFeatures.ClientName.get -> bool
StackExchange.Redis.RedisFeatures.ExecAbort.get -> bool
StackExchange.Redis.RedisFeatures.ExpireOverwrite.get -> bool
StackExchange.Redis.RedisFeatures.Geo.get -> bool
StackExchange.Redis.RedisFeatures.GetDelete.get -> bool
StackExchange.Redis.RedisFeatures.HashStringLength.get -> bool
StackExchange.Redis.RedisFeatures.HashVaradicDelete.get -> bool
StackExchange.Redis.RedisFeatures.HyperLogLogCountReplicaSafe.get -> bool
StackExchange.Redis.RedisFeatures.HyperLogLogCountSlaveSafe.get -> bool
StackExchange.Redis.RedisFeatures.IncrementFloat.get -> bool
StackExchange.Redis.RedisFeatures.InfoSections.get -> bool
StackExchange.Redis.RedisFeatures.KeyTouch.get -> bool
StackExchange.Redis.RedisFeatures.ListInsert.get -> bool
StackExchange.Redis.RedisFeatures.Memory.get -> bool
StackExchange.Redis.RedisFeatures.MillisecondExpiry.get -> bool
StackExchange.Redis.RedisFeatures.Module.get -> bool
StackExchange.Redis.RedisFeatures.MultipleRandom.get -> bool
StackExchange.Redis.RedisFeatures.Persist.get -> bool
StackExchange.Redis.RedisFeatures.PushIfNotExists.get -> bool
StackExchange.Redis.RedisFeatures.PushMultiple.get -> bool
StackExchange.Redis.RedisFeatures.RedisFeatures() -> void
StackExchange.Redis.RedisFeatures.RedisFeatures(System.Version! version) -> void
StackExchange.Redis.RedisFeatures.ReplicaCommands.get -> bool
StackExchange.Redis.RedisFeatures.Scan.get -> bool
StackExchange.Redis.RedisFeatures.Scripting.get -> bool
StackExchange.Redis.RedisFeatures.ScriptingDatabaseSafe.get -> bool
StackExchange.Redis.RedisFeatures.SetAndGet.get -> bool
StackExchange.Redis.RedisFeatures.SetConditional.get -> bool
StackExchange.Redis.RedisFeatures.SetKeepTtl.get -> bool
StackExchange.Redis.RedisFeatures.SetNotExistsAndGet.get -> bool
StackExchange.Redis.RedisFeatures.SetPopMultiple.get -> bool
StackExchange.Redis.RedisFeatures.SetVaradicAddRemove.get -> bool
StackExchange.Redis.RedisFeatures.SortedSetPop.get -> bool
StackExchange.Redis.RedisFeatures.SortedSetRangeStore.get -> bool
StackExchange.Redis.RedisFeatures.Streams.get -> bool
StackExchange.Redis.RedisFeatures.StringLength.get -> bool
StackExchange.Redis.RedisFeatures.StringSetRange.get -> bool
StackExchange.Redis.RedisFeatures.SwapDB.get -> bool
StackExchange.Redis.RedisFeatures.Time.get -> bool
StackExchange.Redis.RedisFeatures.Unlink.get -> bool
StackExchange.Redis.RedisFeatures.Version.get -> System.Version!
StackExchange.Redis.RedisKey
StackExchange.Redis.RedisKey.Append(StackExchange.Redis.RedisKey suffix) -> StackExchange.Redis.RedisKey
StackExchange.Redis.RedisKey.Equals(StackExchange.Redis.RedisKey other) -> bool
StackExchange.Redis.RedisKey.Prepend(StackExchange.Redis.RedisKey prefix) -> StackExchange.Redis.RedisKey
StackExchange.Redis.RedisKey.RedisKey() -> void
StackExchange.Redis.RedisKey.RedisKey(string? key) -> void
StackExchange.Redis.RedisResult
StackExchange.Redis.RedisResult.RedisResult() -> void
StackExchange.Redis.RedisResult.ToDictionary(System.Collections.Generic.IEqualityComparer<string!>? comparer = null) -> System.Collections.Generic.Dictionary<string!, StackExchange.Redis.RedisResult!>!
StackExchange.Redis.RedisServerException
StackExchange.Redis.RedisServerException.RedisServerException(string! message) -> void
StackExchange.Redis.RedisStream
StackExchange.Redis.RedisStream.Entries.get -> StackExchange.Redis.StreamEntry[]!
StackExchange.Redis.RedisStream.Key.get -> StackExchange.Redis.RedisKey
StackExchange.Redis.RedisStream.RedisStream() -> void
StackExchange.Redis.RedisTimeoutException
StackExchange.Redis.RedisTimeoutException.Commandstatus.get -> StackExchange.Redis.CommandStatus
StackExchange.Redis.RedisTimeoutException.RedisTimeoutException(string! message, StackExchange.Redis.CommandStatus commandStatus) -> void
StackExchange.Redis.RedisType
StackExchange.Redis.RedisType.Hash = 5 -> StackExchange.Redis.RedisType
StackExchange.Redis.RedisType.List = 2 -> StackExchange.Redis.RedisType
StackExchange.Redis.RedisType.None = 0 -> StackExchange.Redis.RedisType
StackExchange.Redis.RedisType.Set = 3 -> StackExchange.Redis.RedisType
StackExchange.Redis.RedisType.SortedSet = 4 -> StackExchange.Redis.RedisType
StackExchange.Redis.RedisType.Stream = 6 -> StackExchange.Redis.RedisType
StackExchange.Redis.RedisType.String = 1 -> StackExchange.Redis.RedisType
StackExchange.Redis.RedisType.Unknown = 7 -> StackExchange.Redis.RedisType
StackExchange.Redis.RedisValue
StackExchange.Redis.RedisValue.Box() -> object?
StackExchange.Redis.RedisValue.CompareTo(StackExchange.Redis.RedisValue other) -> int
StackExchange.Redis.RedisValue.Equals(StackExchange.Redis.RedisValue other) -> bool
StackExchange.Redis.RedisValue.HasValue.get -> bool
StackExchange.Redis.RedisValue.IsInteger.get -> bool
StackExchange.Redis.RedisValue.IsNull.get -> bool
StackExchange.Redis.RedisValue.IsNullOrEmpty.get -> bool
StackExchange.Redis.RedisValue.Length() -> long
StackExchange.Redis.RedisValue.RedisValue() -> void
StackExchange.Redis.RedisValue.RedisValue(string! value) -> void
StackExchange.Redis.RedisValue.StartsWith(StackExchange.Redis.RedisValue value) -> bool
StackExchange.Redis.RedisValue.TryParse(out double val) -> bool
StackExchange.Redis.RedisValue.TryParse(out int val) -> bool
StackExchange.Redis.RedisValue.TryParse(out long val) -> bool
StackExchange.Redis.RedisValueWithExpiry
StackExchange.Redis.RedisValueWithExpiry.Expiry.get -> System.TimeSpan?
StackExchange.Redis.RedisValueWithExpiry.RedisValueWithExpiry() -> void
StackExchange.Redis.RedisValueWithExpiry.RedisValueWithExpiry(StackExchange.Redis.RedisValue value, System.TimeSpan? expiry) -> void
StackExchange.Redis.RedisValueWithExpiry.Value.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.ReplicationChangeOptions
StackExchange.Redis.ReplicationChangeOptions.All = StackExchange.Redis.ReplicationChangeOptions.SetTiebreaker | StackExchange.Redis.ReplicationChangeOptions.Broadcast | StackExchange.Redis.ReplicationChangeOptions.EnslaveSubordinates -> StackExchange.Redis.ReplicationChangeOptions
StackExchange.Redis.ReplicationChangeOptions.Broadcast = 2 -> StackExchange.Redis.ReplicationChangeOptions
StackExchange.Redis.ReplicationChangeOptions.EnslaveSubordinates = 4 -> StackExchange.Redis.ReplicationChangeOptions
StackExchange.Redis.ReplicationChangeOptions.None = 0 -> StackExchange.Redis.ReplicationChangeOptions
StackExchange.Redis.ReplicationChangeOptions.ReplicateToOtherEndpoints = 4 -> StackExchange.Redis.ReplicationChangeOptions
StackExchange.Redis.ReplicationChangeOptions.SetTiebreaker = 1 -> StackExchange.Redis.ReplicationChangeOptions
StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.BulkString = 4 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Error = 2 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Integer = 3 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.MultiBulk = 5 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.None = 0 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.SimpleString = 1 -> StackExchange.Redis.ResultType
StackExchange.Redis.RetransmissionReasonType
StackExchange.Redis.RetransmissionReasonType.Ask = 1 -> StackExchange.Redis.RetransmissionReasonType
StackExchange.Redis.RetransmissionReasonType.Moved = 2 -> StackExchange.Redis.RetransmissionReasonType
StackExchange.Redis.RetransmissionReasonType.None = 0 -> StackExchange.Redis.RetransmissionReasonType
StackExchange.Redis.Role
StackExchange.Redis.Role.Master
StackExchange.Redis.Role.Master.Replica
StackExchange.Redis.Role.Master.Replica.Ip.get -> string!
StackExchange.Redis.Role.Master.Replica.Port.get -> int
StackExchange.Redis.Role.Master.Replica.Replica() -> void
StackExchange.Redis.Role.Master.Replica.ReplicationOffset.get -> long
StackExchange.Redis.Role.Master.Replicas.get -> System.Collections.Generic.ICollection<StackExchange.Redis.Role.Master.Replica>!
StackExchange.Redis.Role.Master.ReplicationOffset.get -> long
StackExchange.Redis.Role.Replica
StackExchange.Redis.Role.Replica.MasterIp.get -> string!
StackExchange.Redis.Role.Replica.MasterPort.get -> int
StackExchange.Redis.Role.Replica.ReplicationOffset.get -> long
StackExchange.Redis.Role.Replica.State.get -> string!
StackExchange.Redis.Role.Sentinel
StackExchange.Redis.Role.Sentinel.MonitoredMasters.get -> System.Collections.Generic.ICollection<string?>!
StackExchange.Redis.Role.Unknown
StackExchange.Redis.Role.Value.get -> string!
StackExchange.Redis.SaveType
StackExchange.Redis.SaveType.BackgroundRewriteAppendOnlyFile = 0 -> StackExchange.Redis.SaveType
StackExchange.Redis.SaveType.BackgroundSave = 1 -> StackExchange.Redis.SaveType
StackExchange.Redis.SaveType.ForegroundSave = 2 -> StackExchange.Redis.SaveType
StackExchange.Redis.ServerCounters
StackExchange.Redis.ServerCounters.EndPoint.get -> System.Net.EndPoint?
StackExchange.Redis.ServerCounters.Interactive.get -> StackExchange.Redis.ConnectionCounters!
StackExchange.Redis.ServerCounters.Other.get -> StackExchange.Redis.ConnectionCounters!
StackExchange.Redis.ServerCounters.ServerCounters(System.Net.EndPoint? endpoint) -> void
StackExchange.Redis.ServerCounters.Subscription.get -> StackExchange.Redis.ConnectionCounters!
StackExchange.Redis.ServerCounters.TotalOutstanding.get -> long
StackExchange.Redis.ServerType
StackExchange.Redis.ServerType.Cluster = 2 -> StackExchange.Redis.ServerType
StackExchange.Redis.ServerType.Envoyproxy = 4 -> StackExchange.Redis.ServerType
StackExchange.Redis.ServerType.Sentinel = 1 -> StackExchange.Redis.ServerType
StackExchange.Redis.ServerType.Standalone = 0 -> StackExchange.Redis.ServerType
StackExchange.Redis.ServerType.Twemproxy = 3 -> StackExchange.Redis.ServerType
StackExchange.Redis.SetOperation
StackExchange.Redis.SetOperation.Difference = 2 -> StackExchange.Redis.SetOperation
StackExchange.Redis.SetOperation.Intersect = 1 -> StackExchange.Redis.SetOperation
StackExchange.Redis.SetOperation.Union = 0 -> StackExchange.Redis.SetOperation
StackExchange.Redis.ShutdownMode
StackExchange.Redis.ShutdownMode.Always = 2 -> StackExchange.Redis.ShutdownMode
StackExchange.Redis.ShutdownMode.Default = 0 -> StackExchange.Redis.ShutdownMode
StackExchange.Redis.ShutdownMode.Never = 1 -> StackExchange.Redis.ShutdownMode
StackExchange.Redis.SlotRange
StackExchange.Redis.SlotRange.CompareTo(StackExchange.Redis.SlotRange other) -> int
StackExchange.Redis.SlotRange.Equals(StackExchange.Redis.SlotRange other) -> bool
StackExchange.Redis.SlotRange.From.get -> int
StackExchange.Redis.SlotRange.SlotRange() -> void
StackExchange.Redis.SlotRange.SlotRange(int from, int to) -> void
StackExchange.Redis.SlotRange.To.get -> int
StackExchange.Redis.SocketManager
StackExchange.Redis.SocketManager.Dispose() -> void
StackExchange.Redis.SocketManager.Name.get -> string!
StackExchange.Redis.SocketManager.SocketManager(string? name = null, int workerCount = 0, StackExchange.Redis.SocketManager.SocketManagerOptions options = StackExchange.Redis.SocketManager.SocketManagerOptions.None) -> void
StackExchange.Redis.SocketManager.SocketManager(string! name) -> void
StackExchange.Redis.SocketManager.SocketManager(string! name, bool useHighPrioritySocketThreads) -> void
StackExchange.Redis.SocketManager.SocketManager(string! name, int workerCount, bool useHighPrioritySocketThreads) -> void
StackExchange.Redis.SocketManager.SocketManagerOptions
StackExchange.Redis.SocketManager.SocketManagerOptions.None = 0 -> StackExchange.Redis.SocketManager.SocketManagerOptions
StackExchange.Redis.SocketManager.SocketManagerOptions.UseHighPrioritySocketThreads = 1 -> StackExchange.Redis.SocketManager.SocketManagerOptions
StackExchange.Redis.SocketManager.SocketManagerOptions.UseThreadPool = 2 -> StackExchange.Redis.SocketManager.SocketManagerOptions
StackExchange.Redis.SortedSetEntry
StackExchange.Redis.SortedSetEntry.CompareTo(object? obj) -> int
StackExchange.Redis.SortedSetEntry.CompareTo(StackExchange.Redis.SortedSetEntry other) -> int
StackExchange.Redis.SortedSetEntry.Element.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.SortedSetEntry.Equals(StackExchange.Redis.SortedSetEntry other) -> bool
StackExchange.Redis.SortedSetEntry.Key.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.SortedSetEntry.Score.get -> double
StackExchange.Redis.SortedSetEntry.SortedSetEntry() -> void
StackExchange.Redis.SortedSetEntry.SortedSetEntry(StackExchange.Redis.RedisValue element, double score) -> void
StackExchange.Redis.SortedSetEntry.Value.get -> double
StackExchange.Redis.SortedSetOrder
StackExchange.Redis.SortedSetOrder.ByLex = 2 -> StackExchange.Redis.SortedSetOrder
StackExchange.Redis.SortedSetOrder.ByRank = 0 -> StackExchange.Redis.SortedSetOrder
StackExchange.Redis.SortedSetOrder.ByScore = 1 -> StackExchange.Redis.SortedSetOrder
StackExchange.Redis.SortedSetPopResult
StackExchange.Redis.SortedSetPopResult.Entries.get -> StackExchange.Redis.SortedSetEntry[]!
StackExchange.Redis.SortedSetPopResult.IsNull.get -> bool
StackExchange.Redis.SortedSetPopResult.Key.get -> StackExchange.Redis.RedisKey
StackExchange.Redis.SortedSetPopResult.SortedSetPopResult() -> void
StackExchange.Redis.SortType
StackExchange.Redis.SortType.Alphabetic = 1 -> StackExchange.Redis.SortType
StackExchange.Redis.SortType.Numeric = 0 -> StackExchange.Redis.SortType
StackExchange.Redis.StreamAutoClaimIdsOnlyResult
StackExchange.Redis.StreamAutoClaimIdsOnlyResult.ClaimedIds.get -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.StreamAutoClaimIdsOnlyResult.DeletedIds.get -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.StreamAutoClaimIdsOnlyResult.IsNull.get -> bool
StackExchange.Redis.StreamAutoClaimIdsOnlyResult.NextStartId.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamAutoClaimIdsOnlyResult.StreamAutoClaimIdsOnlyResult() -> void
StackExchange.Redis.StreamAutoClaimResult
StackExchange.Redis.StreamAutoClaimResult.ClaimedEntries.get -> StackExchange.Redis.StreamEntry[]!
StackExchange.Redis.StreamAutoClaimResult.DeletedIds.get -> StackExchange.Redis.RedisValue[]!
StackExchange.Redis.StreamAutoClaimResult.IsNull.get -> bool
StackExchange.Redis.StreamAutoClaimResult.NextStartId.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamAutoClaimResult.StreamAutoClaimResult() -> void
StackExchange.Redis.StreamConsumer
StackExchange.Redis.StreamConsumer.Name.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamConsumer.PendingMessageCount.get -> int
StackExchange.Redis.StreamConsumer.StreamConsumer() -> void
StackExchange.Redis.StreamConsumerInfo
StackExchange.Redis.StreamConsumerInfo.IdleTimeInMilliseconds.get -> long
StackExchange.Redis.StreamConsumerInfo.Name.get -> string!
StackExchange.Redis.StreamConsumerInfo.PendingMessageCount.get -> int
StackExchange.Redis.StreamConsumerInfo.StreamConsumerInfo() -> void
StackExchange.Redis.StreamEntry
StackExchange.Redis.StreamEntry.Id.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamEntry.IsNull.get -> bool
StackExchange.Redis.StreamEntry.StreamEntry() -> void
StackExchange.Redis.StreamEntry.StreamEntry(StackExchange.Redis.RedisValue id, StackExchange.Redis.NameValueEntry[]! values) -> void
StackExchange.Redis.StreamEntry.this[StackExchange.Redis.RedisValue fieldName].get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamEntry.Values.get -> StackExchange.Redis.NameValueEntry[]!
StackExchange.Redis.StreamGroupInfo
StackExchange.Redis.StreamGroupInfo.ConsumerCount.get -> int
StackExchange.Redis.StreamGroupInfo.EntriesRead.get -> long?
StackExchange.Redis.StreamGroupInfo.Lag.get -> long?
StackExchange.Redis.StreamGroupInfo.LastDeliveredId.get -> string?
StackExchange.Redis.StreamGroupInfo.Name.get -> string!
StackExchange.Redis.StreamGroupInfo.PendingMessageCount.get -> int
StackExchange.Redis.StreamGroupInfo.StreamGroupInfo() -> void
StackExchange.Redis.StreamInfo
StackExchange.Redis.StreamInfo.ConsumerGroupCount.get -> int
StackExchange.Redis.StreamInfo.FirstEntry.get -> StackExchange.Redis.StreamEntry
StackExchange.Redis.StreamInfo.LastEntry.get -> StackExchange.Redis.StreamEntry
StackExchange.Redis.StreamInfo.LastGeneratedId.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamInfo.Length.get -> int
StackExchange.Redis.StreamInfo.RadixTreeKeys.get -> int
StackExchange.Redis.StreamInfo.RadixTreeNodes.get -> int
StackExchange.Redis.StreamInfo.StreamInfo() -> void
StackExchange.Redis.StreamPendingInfo
StackExchange.Redis.StreamPendingInfo.Consumers.get -> StackExchange.Redis.StreamConsumer[]!
StackExchange.Redis.StreamPendingInfo.HighestPendingMessageId.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamPendingInfo.LowestPendingMessageId.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamPendingInfo.PendingMessageCount.get -> int
StackExchange.Redis.StreamPendingInfo.StreamPendingInfo() -> void
StackExchange.Redis.StreamPendingMessageInfo
StackExchange.Redis.StreamPendingMessageInfo.ConsumerName.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamPendingMessageInfo.DeliveryCount.get -> int
StackExchange.Redis.StreamPendingMessageInfo.IdleTimeInMilliseconds.get -> long
StackExchange.Redis.StreamPendingMessageInfo.MessageId.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamPendingMessageInfo.StreamPendingMessageInfo() -> void
StackExchange.Redis.StreamPosition
StackExchange.Redis.StreamPosition.Key.get -> StackExchange.Redis.RedisKey
StackExchange.Redis.StreamPosition.Position.get -> StackExchange.Redis.RedisValue
StackExchange.Redis.StreamPosition.StreamPosition() -> void
StackExchange.Redis.StreamPosition.StreamPosition(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue position) -> void
StackExchange.Redis.StringIndexType
StackExchange.Redis.StringIndexType.Byte = 0 -> StackExchange.Redis.StringIndexType
StackExchange.Redis.StringIndexType.Bit = 1 -> StackExchange.Redis.StringIndexType
StackExchange.Redis.SortedSetWhen
StackExchange.Redis.SortedSetWhen.Always = 0 -> StackExchange.Redis.SortedSetWhen
StackExchange.Redis.SortedSetWhen.Exists = 1 -> StackExchange.Redis.SortedSetWhen
StackExchange.Redis.SortedSetWhen.GreaterThan = 2 -> StackExchange.Redis.SortedSetWhen
StackExchange.Redis.SortedSetWhen.LessThan = 4 -> StackExchange.Redis.SortedSetWhen
StackExchange.Redis.SortedSetWhen.NotExists = 8 -> StackExchange.Redis.SortedSetWhen
StackExchange.Redis.When
StackExchange.Redis.When.Always = 0 -> StackExchange.Redis.When
StackExchange.Redis.When.Exists = 1 -> StackExchange.Redis.When
StackExchange.Redis.When.NotExists = 2 -> StackExchange.Redis.When
static StackExchange.Redis.BacklogPolicy.Default.get -> StackExchange.Redis.BacklogPolicy!
static StackExchange.Redis.BacklogPolicy.FailFast.get -> StackExchange.Redis.BacklogPolicy!
static StackExchange.Redis.ChannelMessage.operator !=(StackExchange.Redis.ChannelMessage left, StackExchange.Redis.ChannelMessage right) -> bool
static StackExchange.Redis.ChannelMessage.operator ==(StackExchange.Redis.ChannelMessage left, StackExchange.Redis.ChannelMessage right) -> bool
static StackExchange.Redis.CommandMap.Create(System.Collections.Generic.Dictionary<string!, string?>? overrides) -> StackExchange.Redis.CommandMap!
static StackExchange.Redis.CommandMap.Create(System.Collections.Generic.HashSet<string!>! commands, bool available = true) -> StackExchange.Redis.CommandMap!
static StackExchange.Redis.CommandMap.Default.get -> StackExchange.Redis.CommandMap!
static StackExchange.Redis.CommandMap.Envoyproxy.get -> StackExchange.Redis.CommandMap!
static StackExchange.Redis.CommandMap.Sentinel.get -> StackExchange.Redis.CommandMap!
static StackExchange.Redis.CommandMap.SSDB.get -> StackExchange.Redis.CommandMap!
static StackExchange.Redis.CommandMap.Twemproxy.get -> StackExchange.Redis.CommandMap!
static StackExchange.Redis.Condition.HashEqual(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.RedisValue value) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.HashExists(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.HashLengthEqual(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.HashLengthGreaterThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.HashLengthLessThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.HashNotEqual(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField, StackExchange.Redis.RedisValue value) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.HashNotExists(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue hashField) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.KeyExists(StackExchange.Redis.RedisKey key) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.KeyNotExists(StackExchange.Redis.RedisKey key) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.ListIndexEqual(StackExchange.Redis.RedisKey key, long index, StackExchange.Redis.RedisValue value) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.ListIndexExists(StackExchange.Redis.RedisKey key, long index) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.ListIndexNotEqual(StackExchange.Redis.RedisKey key, long index, StackExchange.Redis.RedisValue value) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.ListIndexNotExists(StackExchange.Redis.RedisKey key, long index) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.ListLengthEqual(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.ListLengthGreaterThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.ListLengthLessThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SetContains(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SetLengthEqual(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SetLengthGreaterThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SetLengthLessThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SetNotContains(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetContains(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetEqual(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.RedisValue score) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetLengthEqual(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetLengthEqual(StackExchange.Redis.RedisKey key, long length, double min = -Infinity, double max = Infinity) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetLengthGreaterThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetLengthGreaterThan(StackExchange.Redis.RedisKey key, long length, double min = -Infinity, double max = Infinity) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetLengthLessThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetLengthLessThan(StackExchange.Redis.RedisKey key, long length, double min = -Infinity, double max = Infinity) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetNotContains(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetNotEqual(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue member, StackExchange.Redis.RedisValue score) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetScoreExists(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue score) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetScoreExists(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue score, StackExchange.Redis.RedisValue count) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetScoreNotExists(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue score) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.SortedSetScoreNotExists(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue score, StackExchange.Redis.RedisValue count) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.StreamLengthEqual(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.StreamLengthGreaterThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.StreamLengthLessThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.StringEqual(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.StringLengthEqual(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.StringLengthGreaterThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.StringLengthLessThan(StackExchange.Redis.RedisKey key, long length) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Condition.StringNotEqual(StackExchange.Redis.RedisKey key, StackExchange.Redis.RedisValue value) -> StackExchange.Redis.Condition!
static StackExchange.Redis.Configuration.DefaultOptionsProvider.AddProvider(StackExchange.Redis.Configuration.DefaultOptionsProvider! provider) -> void
static StackExchange.Redis.Configuration.DefaultOptionsProvider.ComputerName.get -> string!
static StackExchange.Redis.Configuration.DefaultOptionsProvider.GetProvider(StackExchange.Redis.EndPointCollection! endpoints) -> StackExchange.Redis.Configuration.DefaultOptionsProvider!
static StackExchange.Redis.Configuration.DefaultOptionsProvider.GetProvider(System.Net.EndPoint! endpoint) -> StackExchange.Redis.Configuration.DefaultOptionsProvider!
static StackExchange.Redis.Configuration.DefaultOptionsProvider.LibraryVersion.get -> string!
static StackExchange.Redis.ConfigurationOptions.Parse(string! configuration) -> StackExchange.Redis.ConfigurationOptions!
static StackExchange.Redis.ConfigurationOptions.Parse(string! configuration, bool ignoreUnknown) -> StackExchange.Redis.ConfigurationOptions!
static StackExchange.Redis.ConnectionMultiplexer.Connect(StackExchange.Redis.ConfigurationOptions! configuration, System.IO.TextWriter? log = null) -> StackExchange.Redis.ConnectionMultiplexer!
static StackExchange.Redis.ConnectionMultiplexer.Connect(string! configuration, System.Action<StackExchange.Redis.ConfigurationOptions!>! configure, System.IO.TextWriter? log = null) -> StackExchange.Redis.ConnectionMultiplexer!
static StackExchange.Redis.ConnectionMultiplexer.Connect(string! configuration, System.IO.TextWriter? log = null) -> StackExchange.Redis.ConnectionMultiplexer!
static StackExchange.Redis.ConnectionMultiplexer.ConnectAsync(StackExchange.Redis.ConfigurationOptions! configuration, System.IO.TextWriter? log = null) -> System.Threading.Tasks.Task<StackExchange.Redis.ConnectionMultiplexer!>!
static StackExchange.Redis.ConnectionMultiplexer.ConnectAsync(string! configuration, System.Action<StackExchange.Redis.ConfigurationOptions!>! configure, System.IO.TextWriter? log = null) -> System.Threading.Tasks.Task<StackExchange.Redis.ConnectionMultiplexer!>!
static StackExchange.Redis.ConnectionMultiplexer.ConnectAsync(string! configuration, System.IO.TextWriter? log = null) -> System.Threading.Tasks.Task<StackExchange.Redis.ConnectionMultiplexer!>!
static StackExchange.Redis.ConnectionMultiplexer.Factory.get -> System.Threading.Tasks.TaskFactory!
static StackExchange.Redis.ConnectionMultiplexer.Factory.set -> void
static StackExchange.Redis.ConnectionMultiplexer.GetFeatureFlag(string! flag) -> bool
static StackExchange.Redis.ConnectionMultiplexer.SentinelConnect(StackExchange.Redis.ConfigurationOptions! configuration, System.IO.TextWriter? log = null) -> StackExchange.Redis.ConnectionMultiplexer!
static StackExchange.Redis.ConnectionMultiplexer.SentinelConnect(string! configuration, System.IO.TextWriter? log = null) -> StackExchange.Redis.ConnectionMultiplexer!
static StackExchange.Redis.ConnectionMultiplexer.SentinelConnectAsync(StackExchange.Redis.ConfigurationOptions! configuration, System.IO.TextWriter? log = null) -> System.Threading.Tasks.Task<StackExchange.Redis.ConnectionMultiplexer!>!
static StackExchange.Redis.ConnectionMultiplexer.SentinelConnectAsync(string! configuration, System.IO.TextWriter? log = null) -> System.Threading.Tasks.Task<StackExchange.Redis.ConnectionMultiplexer!>!
static StackExchange.Redis.ConnectionMultiplexer.SetFeatureFlag(string! flag, bool enabled) -> void
static StackExchange.Redis.EndPointCollection.ToString(System.Net.EndPoint? endpoint) -> string!
static StackExchange.Redis.EndPointCollection.TryParse(string! endpoint) -> System.Net.EndPoint?
static StackExchange.Redis.ExtensionMethods.AsStream(this StackExchange.Redis.Lease<byte>? bytes, bool ownsLease = true) -> System.IO.Stream?
static StackExchange.Redis.ExtensionMethods.DecodeLease(this StackExchange.Redis.Lease<byte>? bytes, System.Text.Encoding? encoding = null) -> StackExchange.Redis.Lease<char>?
static StackExchange.Redis.ExtensionMethods.DecodeString(this StackExchange.Redis.Lease<byte>! bytes, System.Text.Encoding? encoding = null) -> string?
static StackExchange.Redis.ExtensionMethods.ToDictionary(this StackExchange.Redis.HashEntry[]? hash) -> System.Collections.Generic.Dictionary<StackExchange.Redis.RedisValue, StackExchange.Redis.RedisValue>?
static StackExchange.Redis.ExtensionMethods.ToDictionary(this StackExchange.Redis.SortedSetEntry[]? sortedSet) -> System.Collections.Generic.Dictionary<StackExchange.Redis.RedisValue, double>?
static StackExchange.Redis.ExtensionMethods.ToDictionary(this System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisKey, StackExchange.Redis.RedisValue>[]? pairs) -> System.Collections.Generic.Dictionary<StackExchange.Redis.RedisKey, StackExchange.Redis.RedisValue>?
static StackExchange.Redis.ExtensionMethods.ToDictionary(this System.Collections.Generic.KeyValuePair<string!, string!>[]? pairs) -> System.Collections.Generic.Dictionary<string!, string!>?
static StackExchange.Redis.ExtensionMethods.ToRedisValueArray(this string![]? values) -> StackExchange.Redis.RedisValue[]?
static StackExchange.Redis.ExtensionMethods.ToStringArray(this StackExchange.Redis.RedisValue[]? values) -> string?[]?
static StackExchange.Redis.ExtensionMethods.ToStringDictionary(this StackExchange.Redis.HashEntry[]? hash) -> System.Collections.Generic.Dictionary<string!, string!>?
static StackExchange.Redis.ExtensionMethods.ToStringDictionary(this StackExchange.Redis.SortedSetEntry[]? sortedSet) -> System.Collections.Generic.Dictionary<string!, double>?
static StackExchange.Redis.ExtensionMethods.ToStringDictionary(this System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisKey, StackExchange.Redis.RedisValue>[]? pairs) -> System.Collections.Generic.Dictionary<string!, string!>?
static StackExchange.Redis.GeoEntry.operator !=(StackExchange.Redis.GeoEntry x, StackExchange.Redis.GeoEntry y) -> bool
static StackExchange.Redis.GeoEntry.operator ==(StackExchange.Redis.GeoEntry x, StackExchange.Redis.GeoEntry y) -> bool
static StackExchange.Redis.GeoPosition.operator !=(StackExchange.Redis.GeoPosition x, StackExchange.Redis.GeoPosition y) -> bool
static StackExchange.Redis.GeoPosition.operator ==(StackExchange.Redis.GeoPosition x, StackExchange.Redis.GeoPosition y) -> bool
static StackExchange.Redis.HashEntry.implicit operator StackExchange.Redis.HashEntry(System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, StackExchange.Redis.RedisValue> value) -> StackExchange.Redis.HashEntry
static StackExchange.Redis.HashEntry.implicit operator System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, StackExchange.Redis.RedisValue>(StackExchange.Redis.HashEntry value) -> System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, StackExchange.Redis.RedisValue>
static StackExchange.Redis.HashEntry.operator !=(StackExchange.Redis.HashEntry x, StackExchange.Redis.HashEntry y) -> bool
static StackExchange.Redis.HashEntry.operator ==(StackExchange.Redis.HashEntry x, StackExchange.Redis.HashEntry y) -> bool
static StackExchange.Redis.KeyspaceIsolation.DatabaseExtensions.WithKeyPrefix(this StackExchange.Redis.IDatabase! database, StackExchange.Redis.RedisKey keyPrefix) -> StackExchange.Redis.IDatabase!
static StackExchange.Redis.Lease<T>.Create(int length, bool clear = true) -> StackExchange.Redis.Lease<T>!
static StackExchange.Redis.Lease<T>.Empty.get -> StackExchange.Redis.Lease<T>!
static StackExchange.Redis.ListPopResult.Null.get -> StackExchange.Redis.ListPopResult
static StackExchange.Redis.LuaScript.GetCachedScriptCount() -> int
static StackExchange.Redis.LuaScript.Prepare(string! script) -> StackExchange.Redis.LuaScript!
static StackExchange.Redis.LuaScript.PurgeCache() -> void
static StackExchange.Redis.NameValueEntry.implicit operator StackExchange.Redis.NameValueEntry(System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, StackExchange.Redis.RedisValue> value) -> StackExchange.Redis.NameValueEntry
static StackExchange.Redis.NameValueEntry.implicit operator System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, StackExchange.Redis.RedisValue>(StackExchange.Redis.NameValueEntry value) -> System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, StackExchange.Redis.RedisValue>
static StackExchange.Redis.NameValueEntry.operator !=(StackExchange.Redis.NameValueEntry x, StackExchange.Redis.NameValueEntry y) -> bool
static StackExchange.Redis.NameValueEntry.operator ==(StackExchange.Redis.NameValueEntry x, StackExchange.Redis.NameValueEntry y) -> bool
static StackExchange.Redis.RedisChannel.implicit operator byte[]?(StackExchange.Redis.RedisChannel key) -> byte[]?
static StackExchange.Redis.RedisChannel.implicit operator StackExchange.Redis.RedisChannel(byte[]? key) -> StackExchange.Redis.RedisChannel
static StackExchange.Redis.RedisChannel.implicit operator StackExchange.Redis.RedisChannel(string! key) -> StackExchange.Redis.RedisChannel
static StackExchange.Redis.RedisChannel.implicit operator string?(StackExchange.Redis.RedisChannel key) -> string?
static StackExchange.Redis.RedisChannel.Literal(byte[]! value) -> StackExchange.Redis.RedisChannel
static StackExchange.Redis.RedisChannel.Literal(string! value) -> StackExchange.Redis.RedisChannel
static StackExchange.Redis.RedisChannel.operator !=(byte[]! x, StackExchange.Redis.RedisChannel y) -> bool
static StackExchange.Redis.RedisChannel.operator !=(StackExchange.Redis.RedisChannel x, byte[]! y) -> bool
static StackExchange.Redis.RedisChannel.operator !=(StackExchange.Redis.RedisChannel x, StackExchange.Redis.RedisChannel y) -> bool
static StackExchange.Redis.RedisChannel.operator !=(StackExchange.Redis.RedisChannel x, string! y) -> bool
static StackExchange.Redis.RedisChannel.operator !=(string! x, StackExchange.Redis.RedisChannel y) -> bool
static StackExchange.Redis.RedisChannel.operator ==(byte[]! x, StackExchange.Redis.RedisChannel y) -> bool
static StackExchange.Redis.RedisChannel.operator ==(StackExchange.Redis.RedisChannel x, byte[]! y) -> bool
static StackExchange.Redis.RedisChannel.operator ==(StackExchange.Redis.RedisChannel x, StackExchange.Redis.RedisChannel y) -> bool
static StackExchange.Redis.RedisChannel.operator ==(StackExchange.Redis.RedisChannel x, string! y) -> bool
static StackExchange.Redis.RedisChannel.operator ==(string! x, StackExchange.Redis.RedisChannel y) -> bool
static StackExchange.Redis.RedisChannel.Pattern(byte[]! value) -> StackExchange.Redis.RedisChannel
static StackExchange.Redis.RedisChannel.Pattern(string! value) -> StackExchange.Redis.RedisChannel
static StackExchange.Redis.RedisChannel.UseImplicitAutoPattern.get -> bool
static StackExchange.Redis.RedisChannel.UseImplicitAutoPattern.set -> void
static StackExchange.Redis.RedisFeatures.operator !=(StackExchange.Redis.RedisFeatures left, StackExchange.Redis.RedisFeatures right) -> bool
static StackExchange.Redis.RedisFeatures.operator ==(StackExchange.Redis.RedisFeatures left, StackExchange.Redis.RedisFeatures right) -> bool
static StackExchange.Redis.RedisKey.implicit operator byte[]?(StackExchange.Redis.RedisKey key) -> byte[]?
static StackExchange.Redis.RedisKey.implicit operator StackExchange.Redis.RedisKey(byte[]? key) -> StackExchange.Redis.RedisKey
static StackExchange.Redis.RedisKey.implicit operator StackExchange.Redis.RedisKey(string? key) -> StackExchange.Redis.RedisKey
static StackExchange.Redis.RedisKey.implicit operator string?(StackExchange.Redis.RedisKey key) -> string?
static StackExchange.Redis.RedisKey.operator !=(byte[]! x, StackExchange.Redis.RedisKey y) -> bool
static StackExchange.Redis.RedisKey.operator !=(StackExchange.Redis.RedisKey x, byte[]! y) -> bool
static StackExchange.Redis.RedisKey.operator !=(StackExchange.Redis.RedisKey x, StackExchange.Redis.RedisKey y) -> bool
static StackExchange.Redis.RedisKey.operator !=(StackExchange.Redis.RedisKey x, string! y) -> bool
static StackExchange.Redis.RedisKey.operator !=(string! x, StackExchange.Redis.RedisKey y) -> bool
static StackExchange.Redis.RedisKey.operator +(StackExchange.Redis.RedisKey x, StackExchange.Redis.RedisKey y) -> StackExchange.Redis.RedisKey
static StackExchange.Redis.RedisKey.operator ==(byte[]! x, StackExchange.Redis.RedisKey y) -> bool
static StackExchange.Redis.RedisKey.operator ==(StackExchange.Redis.RedisKey x, byte[]! y) -> bool
static StackExchange.Redis.RedisKey.operator ==(StackExchange.Redis.RedisKey x, StackExchange.Redis.RedisKey y) -> bool
static StackExchange.Redis.RedisKey.operator ==(StackExchange.Redis.RedisKey x, string! y) -> bool
static StackExchange.Redis.RedisKey.operator ==(string! x, StackExchange.Redis.RedisKey y) -> bool
static StackExchange.Redis.RedisResult.Create(StackExchange.Redis.RedisChannel channel) -> StackExchange.Redis.RedisResult!
static StackExchange.Redis.RedisResult.Create(StackExchange.Redis.RedisKey key) -> StackExchange.Redis.RedisResult!
static StackExchange.Redis.RedisResult.Create(StackExchange.Redis.RedisResult![]! values) -> StackExchange.Redis.RedisResult!
static StackExchange.Redis.RedisResult.Create(StackExchange.Redis.RedisValue value, StackExchange.Redis.ResultType? resultType = null) -> StackExchange.Redis.RedisResult!
static StackExchange.Redis.RedisResult.Create(StackExchange.Redis.RedisValue[]! values) -> StackExchange.Redis.RedisResult!
static StackExchange.Redis.RedisResult.explicit operator bool(StackExchange.Redis.RedisResult! result) -> bool
static StackExchange.Redis.RedisResult.explicit operator bool?(StackExchange.Redis.RedisResult? result) -> bool?
static StackExchange.Redis.RedisResult.explicit operator bool[]?(StackExchange.Redis.RedisResult? result) -> bool[]?
static StackExchange.Redis.RedisResult.explicit operator byte[]?(StackExchange.Redis.RedisResult? result) -> byte[]?
static StackExchange.Redis.RedisResult.explicit operator byte[]?[]?(StackExchange.Redis.RedisResult? result) -> byte[]?[]?
static StackExchange.Redis.RedisResult.explicit operator double(StackExchange.Redis.RedisResult! result) -> double
static StackExchange.Redis.RedisResult.explicit operator double?(StackExchange.Redis.RedisResult? result) -> double?
static StackExchange.Redis.RedisResult.explicit operator double[]?(StackExchange.Redis.RedisResult? result) -> double[]?
static StackExchange.Redis.RedisResult.explicit operator int(StackExchange.Redis.RedisResult! result) -> int
static StackExchange.Redis.RedisResult.explicit operator int?(StackExchange.Redis.RedisResult? result) -> int?
static StackExchange.Redis.RedisResult.explicit operator int[]?(StackExchange.Redis.RedisResult? result) -> int[]?
static StackExchange.Redis.RedisResult.explicit operator long(StackExchange.Redis.RedisResult! result) -> long
static StackExchange.Redis.RedisResult.explicit operator long?(StackExchange.Redis.RedisResult? result) -> long?
static StackExchange.Redis.RedisResult.explicit operator long[]?(StackExchange.Redis.RedisResult? result) -> long[]?
static StackExchange.Redis.RedisResult.explicit operator StackExchange.Redis.RedisKey(StackExchange.Redis.RedisResult? result) -> StackExchange.Redis.RedisKey
static StackExchange.Redis.RedisResult.explicit operator StackExchange.Redis.RedisKey[]?(StackExchange.Redis.RedisResult? result) -> StackExchange.Redis.RedisKey[]?
static StackExchange.Redis.RedisResult.explicit operator StackExchange.Redis.RedisResult![]?(StackExchange.Redis.RedisResult? result) -> StackExchange.Redis.RedisResult![]?
static StackExchange.Redis.RedisResult.explicit operator StackExchange.Redis.RedisValue(StackExchange.Redis.RedisResult? result) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisResult.explicit operator StackExchange.Redis.RedisValue[]?(StackExchange.Redis.RedisResult? result) -> StackExchange.Redis.RedisValue[]?
static StackExchange.Redis.RedisResult.explicit operator string?(StackExchange.Redis.RedisResult? result) -> string?
static StackExchange.Redis.RedisResult.explicit operator string?[]?(StackExchange.Redis.RedisResult? result) -> string?[]?
static StackExchange.Redis.RedisResult.explicit operator ulong(StackExchange.Redis.RedisResult! result) -> ulong
static StackExchange.Redis.RedisResult.explicit operator ulong?(StackExchange.Redis.RedisResult? result) -> ulong?
static StackExchange.Redis.RedisResult.explicit operator ulong[]?(StackExchange.Redis.RedisResult? result) -> ulong[]?
static StackExchange.Redis.RedisValue.CreateFrom(System.IO.MemoryStream! stream) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.EmptyString.get -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.explicit operator bool(StackExchange.Redis.RedisValue value) -> bool
static StackExchange.Redis.RedisValue.explicit operator bool?(StackExchange.Redis.RedisValue value) -> bool?
static StackExchange.Redis.RedisValue.explicit operator decimal(StackExchange.Redis.RedisValue value) -> decimal
static StackExchange.Redis.RedisValue.explicit operator decimal?(StackExchange.Redis.RedisValue value) -> decimal?
static StackExchange.Redis.RedisValue.explicit operator double(StackExchange.Redis.RedisValue value) -> double
static StackExchange.Redis.RedisValue.explicit operator double?(StackExchange.Redis.RedisValue value) -> double?
static StackExchange.Redis.RedisValue.explicit operator float(StackExchange.Redis.RedisValue value) -> float
static StackExchange.Redis.RedisValue.explicit operator float?(StackExchange.Redis.RedisValue value) -> float?
static StackExchange.Redis.RedisValue.explicit operator int(StackExchange.Redis.RedisValue value) -> int
static StackExchange.Redis.RedisValue.explicit operator int?(StackExchange.Redis.RedisValue value) -> int?
static StackExchange.Redis.RedisValue.explicit operator long(StackExchange.Redis.RedisValue value) -> long
static StackExchange.Redis.RedisValue.explicit operator long?(StackExchange.Redis.RedisValue value) -> long?
static StackExchange.Redis.RedisValue.explicit operator uint(StackExchange.Redis.RedisValue value) -> uint
static StackExchange.Redis.RedisValue.explicit operator uint?(StackExchange.Redis.RedisValue value) -> uint?
static StackExchange.Redis.RedisValue.explicit operator ulong(StackExchange.Redis.RedisValue value) -> ulong
static StackExchange.Redis.RedisValue.explicit operator ulong?(StackExchange.Redis.RedisValue value) -> ulong?
static StackExchange.Redis.RedisValue.implicit operator byte[]?(StackExchange.Redis.RedisValue value) -> byte[]?
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(bool value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(bool? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(byte[]? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(double value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(double? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(int value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(int? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(long value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(long? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(string? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(System.Memory<byte> value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(System.ReadOnlyMemory<byte> value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(uint value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(uint? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(ulong value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator StackExchange.Redis.RedisValue(ulong? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.implicit operator string?(StackExchange.Redis.RedisValue value) -> string?
static StackExchange.Redis.RedisValue.implicit operator System.ReadOnlyMemory<byte>(StackExchange.Redis.RedisValue value) -> System.ReadOnlyMemory<byte>
static StackExchange.Redis.RedisValue.Null.get -> StackExchange.Redis.RedisValue
static StackExchange.Redis.RedisValue.operator !=(StackExchange.Redis.RedisValue x, StackExchange.Redis.RedisValue y) -> bool
static StackExchange.Redis.RedisValue.operator ==(StackExchange.Redis.RedisValue x, StackExchange.Redis.RedisValue y) -> bool
static StackExchange.Redis.RedisValue.Unbox(object? value) -> StackExchange.Redis.RedisValue
static StackExchange.Redis.SlotRange.operator !=(StackExchange.Redis.SlotRange x, StackExchange.Redis.SlotRange y) -> bool
static StackExchange.Redis.SlotRange.operator ==(StackExchange.Redis.SlotRange x, StackExchange.Redis.SlotRange y) -> bool
static StackExchange.Redis.SlotRange.TryParse(string! range, out StackExchange.Redis.SlotRange value) -> bool
static StackExchange.Redis.SocketManager.Shared.get -> StackExchange.Redis.SocketManager!
static StackExchange.Redis.SocketManager.ThreadPool.get -> StackExchange.Redis.SocketManager!
static StackExchange.Redis.SortedSetEntry.implicit operator StackExchange.Redis.SortedSetEntry(System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, double> value) -> StackExchange.Redis.SortedSetEntry
static StackExchange.Redis.SortedSetEntry.implicit operator System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, double>(StackExchange.Redis.SortedSetEntry value) -> System.Collections.Generic.KeyValuePair<StackExchange.Redis.RedisValue, double>
static StackExchange.Redis.SortedSetEntry.operator !=(StackExchange.Redis.SortedSetEntry x, StackExchange.Redis.SortedSetEntry y) -> bool
static StackExchange.Redis.SortedSetEntry.operator ==(StackExchange.Redis.SortedSetEntry x, StackExchange.Redis.SortedSetEntry y) -> bool
static StackExchange.Redis.SortedSetPopResult.Null.get -> StackExchange.Redis.SortedSetPopResult
static StackExchange.Redis.StreamAutoClaimIdsOnlyResult.Null.get -> StackExchange.Redis.StreamAutoClaimIdsOnlyResult
static StackExchange.Redis.StreamAutoClaimResult.Null.get -> StackExchange.Redis.StreamAutoClaimResult
static StackExchange.Redis.StreamEntry.Null.get -> StackExchange.Redis.StreamEntry
static StackExchange.Redis.StreamPosition.Beginning.get -> StackExchange.Redis.RedisValue
static StackExchange.Redis.StreamPosition.NewMessages.get -> StackExchange.Redis.RedisValue
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.AbortOnConnectFail.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.AfterConnectAsync(StackExchange.Redis.ConnectionMultiplexer! multiplexer, System.Action<string!>! log) -> System.Threading.Tasks.Task!
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.AllowAdmin.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.BacklogPolicy.get -> StackExchange.Redis.BacklogPolicy!
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.CheckCertificateRevocation.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.CommandMap.get -> StackExchange.Redis.CommandMap?
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.ConfigCheckInterval.get -> System.TimeSpan
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.ConfigurationChannel.get -> string!
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.ConnectRetry.get -> int
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.ConnectTimeout.get -> System.TimeSpan?
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.DefaultVersion.get -> System.Version!
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.GetDefaultClientName() -> string!
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.GetDefaultSsl(StackExchange.Redis.EndPointCollection! endPoints) -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.GetSslHostFromEndpoints(StackExchange.Redis.EndPointCollection! endPoints) -> string?
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.HeartbeatConsistencyChecks.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.HeartbeatInterval.get -> System.TimeSpan
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.HighIntegrity.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.IncludeDetailInExceptions.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.IncludePerformanceCountersInExceptions.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.IsMatch(System.Net.EndPoint! endpoint) -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.KeepAliveInterval.get -> System.TimeSpan
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.LibraryName.get -> string!
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.LoggerFactory.get -> Microsoft.Extensions.Logging.ILoggerFactory?
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.Password.get -> string?
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.Proxy.get -> StackExchange.Redis.Proxy
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.ReconnectRetryPolicy.get -> StackExchange.Redis.IReconnectRetryPolicy?
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.ResolveDns.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.SetClientLibrary.get -> bool
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.SyncTimeout.get -> System.TimeSpan
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.TieBreaker.get -> string!
virtual StackExchange.Redis.Configuration.DefaultOptionsProvider.User.get -> string?
abstract StackExchange.Redis.RedisResult.ToString(out string? type) -> string?
override sealed StackExchange.Redis.RedisResult.ToString() -> string!
override StackExchange.Redis.Role.Master.Replica.ToString() -> string!
StackExchange.Redis.ClientInfo.Protocol.get -> StackExchange.Redis.RedisProtocol?
StackExchange.Redis.ConfigurationOptions.Protocol.get -> StackExchange.Redis.RedisProtocol?
StackExchange.Redis.ConfigurationOptions.Protocol.set -> void
StackExchange.Redis.IServer.Protocol.get -> StackExchange.Redis.RedisProtocol
StackExchange.Redis.RedisFeatures.ClientId.get -> bool
StackExchange.Redis.RedisFeatures.Equals(StackExchange.Redis.RedisFeatures other) -> bool
StackExchange.Redis.RedisFeatures.Resp3.get -> bool
StackExchange.Redis.RedisProtocol
StackExchange.Redis.RedisProtocol.Resp2 = 20000 -> StackExchange.Redis.RedisProtocol
StackExchange.Redis.RedisProtocol.Resp3 = 30000 -> StackExchange.Redis.RedisProtocol
StackExchange.Redis.RedisResult.Resp2Type.get -> StackExchange.Redis.ResultType
StackExchange.Redis.RedisResult.Resp3Type.get -> StackExchange.Redis.ResultType
StackExchange.Redis.RedisResult.Type.get -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Array = 5 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Attribute = 29 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.BigInteger = 17 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.BlobError = 10 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Boolean = 11 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Double = 9 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Map = 13 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Null = 8 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Push = 37 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.Set = 21 -> StackExchange.Redis.ResultType
StackExchange.Redis.ResultType.VerbatimString = 12 -> StackExchange.Redis.ResultType
static StackExchange.Redis.RedisResult.Create(StackExchange.Redis.RedisResult![]! values, StackExchange.Redis.ResultType resultType) -> StackExchange.Redis.RedisResult!
static StackExchange.Redis.RedisResult.Create(StackExchange.Redis.RedisValue[]! values, StackExchange.Redis.ResultType resultType) -> StackExchange.Redis.RedisResult!
virtual StackExchange.Redis.RedisResult.Length.get -> int
virtual StackExchange.Redis.RedisResult.this[int index].get -> StackExchange.Redis.RedisResult!
StackExchange.Redis.ConnectionMultiplexer.AddLibraryNameSuffix(string! suffix) -> void
StackExchange.Redis.IConnectionMultiplexer.AddLibraryNameSuffix(string! suffix) -> void
StackExchange.Redis.RedisFeatures.ShardedPubSub.get -> bool
static StackExchange.Redis.RedisChannel.Sharded(byte[]? value) -> StackExchange.Redis.RedisChannel
static StackExchange.Redis.RedisChannel.Sharded(string! value) -> StackExchange.Redis.RedisChannel
StackExchange.Redis.ClientInfo.ShardedSubscriptionCount.get -> int
StackExchange.Redis.ConfigurationOptions.SetUserPfxCertificate(string! userCertificatePath, string? password = null) -> void
