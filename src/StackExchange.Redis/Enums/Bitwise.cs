﻿namespace StackExchange.Redis
{
    /// <summary>
    /// <a href="https://en.wikipedia.org/wiki/Bitwise_operation">Bitwise operators</a>
    /// </summary>
    public enum Bitwise
    {
        /// <summary>
        /// <a href="https://en.wikipedia.org/wiki/Bitwise_operation#AND">And</a>
        /// </summary>
        And,

        /// <summary>
        /// <a href="https://en.wikipedia.org/wiki/Bitwise_operation#OR">Or</a>
        /// </summary>
        Or,

        /// <summary>
        /// <a href="https://en.wikipedia.org/wiki/Bitwise_operation#XOR">Xor</a>
        /// </summary>
        Xor,

        /// <summary>
        /// <a href="https://en.wikipedia.org/wiki/Bitwise_operation#NOT">Not</a>
        /// </summary>
        Not,
    }
}
