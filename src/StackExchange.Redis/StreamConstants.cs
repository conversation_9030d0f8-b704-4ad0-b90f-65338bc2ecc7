﻿namespace StackExchange.Redis
{
    /// <summary>
    /// Constants representing values used in Redis Stream commands.
    /// </summary>
    internal static class StreamConstants
    {
        /// <summary>
        /// The "~" value used with the MAXLEN option.
        /// </summary>
        internal static readonly RedisValue ApproximateMaxLen = "~";

        /// <summary>
        /// The "*" value used with the XADD command.
        /// </summary>
        internal static readonly RedisValue AutoGeneratedId = "*";

        /// <summary>
        /// The "$" value used in the XGROUP command. Indicates reading only new messages from the stream.
        /// </summary>
        internal static readonly RedisValue NewMessages = "$";

        /// <summary>
        /// The "0" value used in the XGROUP command. Indicates reading all messages from the stream.
        /// </summary>
        internal static readonly RedisValue AllMessages = "0";

        /// <summary>
        /// The "-" value used in the XRANGE, XREAD, and XREADGROUP commands. Indicates the minimum message ID from the stream.
        /// </summary>
        internal static readonly RedisValue ReadMinValue = "-";

        /// <summary>
        /// The "+" value used in the XRANGE, XREAD, and XREADGROUP commands. Indicates the maximum message ID from the stream.
        /// </summary>
        internal static readonly RedisValue ReadMaxValue = "+";

        /// <summary>
        /// The ">" value used in the XREADGROUP command. Use this to read messages that have not been delivered to a consumer group.
        /// </summary>
        internal static readonly RedisValue UndeliveredMessages = ">";

        internal static readonly RedisValue Consumers = "CONSUMERS";

        internal static readonly RedisValue Count = "COUNT";

        internal static readonly RedisValue Create = "CREATE";

        internal static readonly RedisValue DeleteConsumer = "DELCONSUMER";

        internal static readonly RedisValue Destroy = "DESTROY";

        internal static readonly RedisValue Group = "GROUP";

        internal static readonly RedisValue Groups = "GROUPS";

        internal static readonly RedisValue JustId = "JUSTID";

        internal static readonly RedisValue SetId = "SETID";

        internal static readonly RedisValue MaxLen = "MAXLEN";

        internal static readonly RedisValue MkStream = "MKSTREAM";

        internal static readonly RedisValue NoAck = "NOACK";

        internal static readonly RedisValue Stream = "STREAM";

        internal static readonly RedisValue Streams = "STREAMS";
    }
}
