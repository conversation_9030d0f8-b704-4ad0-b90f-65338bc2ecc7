The MIT License (MIT)

Copyright (c) 2014 Stack Exchange

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

===============================================

Third Party Licenses:

The Redis project (https://redis.io/) is independent of this client library, and
is licensed separately under the three clause BSD license. The full license
information can be viewed here: https://redis.io/topics/license

This tool makes use of the "redis-doc" library from https://redis.io/documentation
in the intellisense comments, which is licensed under the
Creative Commons Attribution-ShareAlike 4.0 International license; full
details are available here:
https://github.com/antirez/redis-doc/blob/master/COPYRIGHT

The development solution uses the Redis-64 package from nuget
(https://www.nuget.org/packages/Redis-64) by Microsoft Open Technologies, inc.
This is licensed under the BSD license; full details are available here:
https://github.com/MSOpenTech/redis/blob/2.6/license.txt
This tool is not used in the release binaries.

The development solution uses the BookSleeve package from nuget
(https://code.google.com/p/booksleeve/) by Marc Gravell. This is licensed
under the Apache 2.0 license; full details are available here:
https://www.apache.org/licenses/LICENSE-2.0
This tool is not used in the release binaries.