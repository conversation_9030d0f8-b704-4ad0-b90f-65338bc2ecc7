<Project>
  <PropertyGroup>
    <VersionPrefix>2.0.0</VersionPrefix>
    <Copyright>2014 - $([System.DateTime]::Now.Year) Stack Exchange, Inc.</Copyright>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <AssemblyOriginatorKeyFile>$(MSBuildThisFileDirectory)StackExchange.Redis.snk</AssemblyOriginatorKeyFile>
    <PackageId>$(AssemblyName)</PackageId>
    <Features>strict</Features>
    <Authors>Stack Exchange, Inc.; <PERSON>; <PERSON></Authors>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <CodeAnalysisRuleset>$(MSBuildThisFileDirectory)Shared.ruleset</CodeAnalysisRuleset>
    <MSBuildWarningsAsMessages>NETSDK1069</MSBuildWarningsAsMessages>
    <NoWarn>NU5105;NU1507</NoWarn>
    <PackageReleaseNotes>https://stackexchange.github.io/StackExchange.Redis/ReleaseNotes</PackageReleaseNotes>
    <PackageProjectUrl>https://stackexchange.github.io/StackExchange.Redis/</PackageProjectUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>

    <LangVersion>11</LangVersion>
    <RepositoryType>git</RepositoryType>
    <RepositoryUrl>https://github.com/StackExchange/StackExchange.Redis/</RepositoryUrl>

    <DebugSymbols>true</DebugSymbols>
    <DebugType>embedded</DebugType>
    <DefaultLanguage>en-US</DefaultLanguage>
    <IncludeSymbols>false</IncludeSymbols>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <IsPackable>false</IsPackable>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <PublicKey>00240000048000009400000006020000002400005253413100040000010001007791a689e9d8950b44a9a8886baad2ea180e7a8a854f158c9b98345ca5009cdd2362c84f368f1c3658c132b3c0f74e44ff16aeb2e5b353b6e0fe02f923a050470caeac2bde47a2238a9c7125ed7dab14f486a5a64558df96640933b9f2b6db188fc4a820f96dce963b662fa8864adbff38e5b4542343f162ecdc6dad16912fff</PublicKey>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <ContinuousIntegrationBuild>true</ContinuousIntegrationBuild>
    <Deterministic>true</Deterministic>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
  </PropertyGroup>

  <ItemGroup Condition="'$(Configuration)' == 'Release' and '$(SourceRoot)'==''">
    <SourceRoot Include="$(MSBuildThisFileDirectory)/"/>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" PrivateAssets="all" />
    <PackageReference Include="StyleCop.Analyzers" PrivateAssets="All" />
  </ItemGroup>
</Project>
