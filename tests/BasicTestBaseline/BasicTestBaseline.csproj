﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Description>StackExchange.Redis.BasicTest .NET Core</Description>
    <TargetFrameworks>net472;net8.0</TargetFrameworks>
    <AssemblyName>BasicTestBaseline</AssemblyName>
    <OutputType>Exe</OutputType>
    <PackageId>BasicTestBaseline</PackageId>
    <!--<RuntimeIdentifiers>win7-x64</RuntimeIdentifiers>-->
    <DefineConstants>$(DefineConstants);TEST_BASELINE</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\BasicTest\Program.cs" Link="Program.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet" />
    <PackageReference Include="StackExchange.Redis" />
    <PackageReference Include="System.Collections.Immutable" />
    <PackageReference Include="System.Reflection.Metadata" />
  </ItemGroup>

</Project>
