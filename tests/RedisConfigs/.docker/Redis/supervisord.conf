[supervisord]
nodaemon=false

[program:primary-6379]
command=/usr/local/bin/redis-server /data/Basic/primary-6379.conf
directory=/data/Basic
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:replica-6380]
command=/usr/local/bin/redis-server /data/Basic/replica-6380.conf
directory=/data/Basic
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:secure-6381]
command=/usr/local/bin/redis-server /data/Basic/secure-6381.conf
directory=/data/Basic
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:tls-6384]
command=/usr/local/bin/redis-server /data/Basic/tls-ciphers-6384.conf
directory=/data/Basic
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:primary-6382]
command=/usr/local/bin/redis-server /data/Failover/primary-6382.conf
directory=/data/Failover
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:replica-6383]
command=/usr/local/bin/redis-server /data/Failover/replica-6383.conf
directory=/data/Failover
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:cluster-7000]
command=/usr/local/bin/redis-server /data/Cluster/cluster-7000.conf
directory=/data/Cluster
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:cluster-7001]
command=/usr/local/bin/redis-server /data/Cluster/cluster-7001.conf
directory=/data/Cluster
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:cluster-7002]
command=/usr/local/bin/redis-server /data/Cluster/cluster-7002.conf
directory=/data/Cluster
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:cluster-7003]
command=/usr/local/bin/redis-server /data/Cluster/cluster-7003.conf
directory=/data/Cluster
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:cluster-7004]
command=/usr/local/bin/redis-server /data/Cluster/cluster-7004.conf
directory=/data/Cluster
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:cluster-7005]
command=/usr/local/bin/redis-server /data/Cluster/cluster-7005.conf
directory=/data/Cluster
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:redis-7010]
command=/usr/local/bin/redis-server /data/Sentinel/redis-7010.conf
directory=/data/Sentinel
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:redis-7011]
command=/usr/local/bin/redis-server /data/Sentinel/redis-7011.conf
directory=/data/Sentinel
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:sentinel-26379]
command=/usr/local/bin/redis-server /data/Sentinel/sentinel-26379.conf --sentinel
directory=/data/Sentinel
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:sentinel-26380]
command=/usr/local/bin/redis-server /data/Sentinel/sentinel-26380.conf --sentinel
directory=/data/Sentinel
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true

[program:sentinel-26381]
command=/usr/local/bin/redis-server /data/Sentinel/sentinel-26381.conf --sentinel
directory=/data/Sentinel
stdout_logfile=/var/log/supervisor/%(program_name)s.log
stderr_logfile=/var/log/supervisor/%(program_name)s.log
autorestart=true