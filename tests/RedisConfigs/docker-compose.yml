version: '2.7'

services:
  redis:
    build:
      context: .docker/Redis
      additional_contexts:
         configs: .
    platform: linux
    ports:
      - 6379-6384:6379-6384     # Misc
      - 7000-7006:7000-7006     # Cluster
      - 7010-7011:7010-7011     # Sentinel Controllers
      - 26379-26381:26379-26381 # Sentinel Data
    sysctls :
      net.core.somaxconn: '511'
  envoy:
    build:
      context: .docker/Envoy
    platform: linux
    environment:
      loglevel: warning
    depends_on:
      redis:
        condition: service_started
    ports:
      - 7015:7015 # Cluster
      - 8001:8001 # Admin
