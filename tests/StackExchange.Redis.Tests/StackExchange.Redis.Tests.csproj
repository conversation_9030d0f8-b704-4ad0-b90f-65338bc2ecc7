﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net481;net8.0</TargetFrameworks>
    <AssemblyName>StackExchange.Redis.Tests</AssemblyName>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <SignAssembly>true</SignAssembly>
    <DebugType>full</DebugType>
    <Nullable>enable</Nullable>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <ItemGroup>
    <None Update="*.json" CopyToOutputDirectory="Always" />
    <EmbeddedResource Include="*Config.json" />
    <None Update="redislabs_ca.pem" CopyToOutputDirectory="PreserveNewest" />
    <None Update="Certificates\*.pem" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\StackExchange.Redis\StackExchange.Redis.csproj" />
    <PackageReference Include="GitHubActionsTestLogger" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="NSubstitute" />
    <PackageReference Include="System.Runtime.InteropServices.RuntimeInformation" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" PrivateAssets="all" />
    <PackageReference Include="System.IO.Compression" />
    <PackageReference Include="System.IO.Pipelines" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'net462' ">
    <Reference Include="Microsoft.CSharp" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="5.0.0" />
  </ItemGroup>
</Project>
