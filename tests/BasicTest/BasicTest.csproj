﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Description>StackExchange.Redis.BasicTest .NET Core</Description>
    <TargetFrameworks>net472;net8.0</TargetFrameworks>
    <AssemblyName>BasicTest</AssemblyName>
    <OutputType>Exe</OutputType>
    <PackageId>BasicTest</PackageId>
    <!--<RuntimeIdentifiers>win7-x64</RuntimeIdentifiers>-->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet" />
    <PackageReference Include="System.Collections.Immutable" />
    <PackageReference Include="System.Reflection.Metadata" />
    
    <ProjectReference Include="..\..\src\StackExchange.Redis\StackExchange.Redis.csproj" />
  </ItemGroup>

</Project>
