StackExchange.Redis
===================

StackExchange.Redis is a .NET client for communicating with RESP servers such as [Redis](https://redis.io/), [Garnet](https://microsoft.github.io/garnet/), [Valkey](https://valkey.io/), [Azure Cache for Redis](https://azure.microsoft.com/products/cache), [AWS ElastiCache](https://aws.amazon.com/elasticache/), and a wide range of other Redis-like servers. We do not maintain a list of compatible servers, but if the server has a Redis-like API: it will *probably* work fine. If not: log an issue with details!

For all documentation, [see here](https://stackexchange.github.io/StackExchange.Redis/).

#### Build Status

[![Build status](https://ci.appveyor.com/api/projects/status/2o3frasprum8mbaj/branch/main?svg=true)](https://ci.appveyor.com/project/StackExchange/stackexchange-redis/branch/main)

#### Package Status

MyGet Pre-release feed: https://www.myget.org/gallery/stackoverflow

| Package | NuGet Stable | NuGet Pre-release | Downloads | MyGet |
| ------- | ------------ | ----------------- | --------- | ----- |
| [StackExchange.Redis](https://www.nuget.org/packages/StackExchange.Redis/) | [![StackExchange.Redis](https://img.shields.io/nuget/v/StackExchange.Redis.svg)](https://www.nuget.org/packages/StackExchange.Redis/) | [![StackExchange.Redis](https://img.shields.io/nuget/vpre/StackExchange.Redis.svg)](https://www.nuget.org/packages/StackExchange.Redis/absoluteLatest) | [![StackExchange.Redis](https://img.shields.io/nuget/dt/StackExchange.Redis.svg)](https://www.nuget.org/packages/StackExchange.Redis/) | [![StackExchange.Redis MyGet](https://img.shields.io/myget/stackoverflow/vpre/StackExchange.Redis.svg)](https://www.myget.org/feed/stackoverflow/package/nuget/StackExchange.Redis) |

Release notes at: https://stackexchange.github.io/StackExchange.Redis/ReleaseNotes
